"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = exports.pool = void 0;
const pg_1 = require("pg");
const logger_1 = require("@/utils/logger");
const getDatabaseConfig = () => {
    if (process.env.DATABASE_URL) {
        return {
            connectionString: process.env.DATABASE_URL,
            max: 20,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
        };
    }
    const config = {
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        database: process.env.DB_NAME || 'mobile_app_builder',
        user: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || '',
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
    };
    if (config.password === undefined || config.password === null) {
        config.password = '';
    }
    return config;
};
const pool = new pg_1.Pool(getDatabaseConfig());
exports.pool = pool;
pool.on('connect', () => {
    logger_1.logger.info('Connected to PostgreSQL database');
});
pool.on('error', (err) => {
    logger_1.logger.error('PostgreSQL connection error:', err);
    process.exit(-1);
});
const initializeDatabase = async () => {
    try {
        const client = await pool.connect();
        await client.query('SELECT NOW()');
        client.release();
        logger_1.logger.info('Database connection established successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to database:', error);
        throw error;
    }
};
exports.initializeDatabase = initializeDatabase;
//# sourceMappingURL=database.js.map