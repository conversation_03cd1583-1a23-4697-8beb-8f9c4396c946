interface BundleRequest {
    projectId: string;
    platform: 'web' | 'ios' | 'android';
    entryFile?: string;
    options?: {
        dev?: boolean;
        minify?: boolean;
        sourceMaps?: boolean;
        hot?: boolean;
    };
}
interface BundleResponse {
    success: boolean;
    bundle?: {
        id: string;
        platform: string;
        code: string;
        sourceMap?: string;
        size: number;
        buildTime: number;
        createdAt: string;
        errors: any[];
        warnings: any[];
    };
    error?: string;
    buildTime: number;
}
interface ValidationRequest {
    projectId: string;
    filePath: string;
    content: string;
}
interface ValidationResponse {
    success: boolean;
    validation: {
        valid: boolean;
        errors: Array<{
            type: string;
            message: string;
            filename: string;
            line: number;
            column: number;
            severity: 'error' | 'warning';
        }>;
        warnings: Array<{
            type: string;
            message: string;
            filename: string;
            line: number;
            column: number;
        }>;
    };
}
export declare class BundlerService {
    private bundles;
    private watchers;
    /**
     * Create bundle for a project
     */
    createBundle(request: BundleRequest): Promise<BundleResponse>;
    /**
     * Get cached bundle
     */
    getCachedBundle(projectId: string, platform: 'web' | 'ios' | 'android'): Promise<BundleResponse>;
    /**
     * Validate code syntax and imports
     */
    validateCode(request: ValidationRequest): Promise<ValidationResponse>;
    /**
     * Start file watching for a project
     */
    startFileWatching(projectId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Stop file watching for a project
     */
    stopFileWatching(projectId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Clear bundle cache for a project
     */
    clearBundleCache(projectId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Cleanup bundler resources for a project
     */
    cleanup(projectId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Get bundler service status
     */
    getStatus(): Promise<any>;
    private generateMockBundle;
}
export {};
