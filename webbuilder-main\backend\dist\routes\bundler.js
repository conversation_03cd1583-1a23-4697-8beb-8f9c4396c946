"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const bundlerService_1 = require("../services/bundlerService");
const router = express_1.default.Router();
const bundlerService = new bundlerService_1.BundlerService();
/**
 * Create bundle for a project
 */
router.post('/build', async (req, res) => {
    try {
        const { projectId, platform, entryFile, options } = req.body;
        if (!projectId || !platform) {
            return res.status(400).json({
                success: false,
                error: 'Missing required parameters: projectId, platform'
            });
        }
        console.log(`Building bundle for project ${projectId} on ${platform}`);
        const result = await bundlerService.createBundle({
            projectId,
            platform,
            entryFile,
            options
        });
        res.json(result);
    }
    catch (error) {
        console.error('Bundle creation error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            buildTime: 0
        });
    }
});
/**
 * Get cached bundle
 */
router.get('/bundle/:projectId/:platform', async (req, res) => {
    try {
        const { projectId, platform } = req.params;
        if (!['web', 'ios', 'android'].includes(platform)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid platform. Must be web, ios, or android'
            });
        }
        const result = await bundlerService.getCachedBundle(projectId, platform);
        if (!result.success) {
            return res.status(404).json(result);
        }
        // Set appropriate headers for bundle serving
        res.setHeader('Content-Type', 'application/javascript');
        res.setHeader('Cache-Control', 'no-cache');
        res.send(result.bundle?.code || '');
    }
    catch (error) {
        console.error('Get cached bundle error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * Validate code syntax and imports
 */
router.post('/validate', async (req, res) => {
    try {
        const { projectId, filePath, content } = req.body;
        if (!projectId || !filePath || content === undefined) {
            return res.status(400).json({
                success: false,
                error: 'Missing required parameters: projectId, filePath, content'
            });
        }
        const result = await bundlerService.validateCode({
            projectId,
            filePath,
            content
        });
        res.json(result);
    }
    catch (error) {
        console.error('Code validation error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * Start file watching for a project
 */
router.post('/watch/start/:projectId', async (req, res) => {
    try {
        const { projectId } = req.params;
        const result = await bundlerService.startFileWatching(projectId);
        res.json(result);
    }
    catch (error) {
        console.error('Start file watching error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * Stop file watching for a project
 */
router.post('/watch/stop/:projectId', async (req, res) => {
    try {
        const { projectId } = req.params;
        const result = await bundlerService.stopFileWatching(projectId);
        res.json(result);
    }
    catch (error) {
        console.error('Stop file watching error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * Clear bundle cache for a project
 */
router.delete('/cache/:projectId', async (req, res) => {
    try {
        const { projectId } = req.params;
        const result = await bundlerService.clearBundleCache(projectId);
        res.json(result);
    }
    catch (error) {
        console.error('Clear bundle cache error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * Get bundler service status
 */
router.get('/status', async (req, res) => {
    try {
        const status = await bundlerService.getStatus();
        res.json(status);
    }
    catch (error) {
        console.error('Get bundler status error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * Cleanup bundler resources for a project
 */
router.delete('/cleanup/:projectId', async (req, res) => {
    try {
        const { projectId } = req.params;
        const result = await bundlerService.cleanup(projectId);
        res.json(result);
    }
    catch (error) {
        console.error('Bundler cleanup error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.default = router;
