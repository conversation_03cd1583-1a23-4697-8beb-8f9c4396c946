# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
# Use SQLite for development (recommended)
DB_TYPE=sqlite
SQLITE_DB_PATH=./data/app.db

# PostgreSQL Configuration (for production)
# DB_TYPE=postgresql
# DATABASE_URL=postgresql://username:password@localhost:5432/mobile_app_builder
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=mobile_app_builder
# DB_USER=postgres
# DB_PASSWORD=""

# Set to true in production to require database connection
REQUIRE_DATABASE=false

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info

# AI Service Configuration
# Get your API keys from:
# OpenAI: https://platform.openai.com/api-keys
# Anthropic: https://console.anthropic.com/
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Pinecone Configuration (optional - for vector search)
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=your_pinecone_index_name

# File Storage Configuration
FILE_STORAGE_PATH=./storage/projects
MAX_FILE_SIZE=10mb
MAX_PROJECT_SIZE=100mb

# Bundler Configuration
BUNDLE_CACHE_TTL=3600
MAX_BUNDLE_SIZE=50mb

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AI_MAX_REQUESTS=20