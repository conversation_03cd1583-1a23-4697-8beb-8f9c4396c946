import { Pool } from 'pg';
import { logger } from '@/utils/logger';

// Database configuration with proper validation
const getDatabaseConfig = () => {
  // If DATABASE_URL is provided, use it (for production/cloud deployments)
  if (process.env.DATABASE_URL) {
    return {
      connectionString: process.env.DATABASE_URL,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    };
  }

  // Otherwise, use individual connection parameters
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'mobile_app_builder',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  };

  // Ensure password is a string (fix for SASL error)
  if (config.password === undefined || config.password === null) {
    config.password = '';
  }

  return config;
};

const pool = new Pool(getDatabaseConfig());

pool.on('connect', () => {
  logger.info('Connected to PostgreSQL database');
});

pool.on('error', (err) => {
  logger.error('PostgreSQL connection error:', err);
  process.exit(-1);
});

export { pool };

export const initializeDatabase = async (): Promise<void> => {
  try {
    // Test the connection
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    logger.info('Database connection established successfully');
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    throw error;
  }
};