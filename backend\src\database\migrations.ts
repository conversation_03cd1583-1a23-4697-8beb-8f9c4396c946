import { db } from '@/config/database';
import { logger } from '@/utils/logger';
import fs from 'fs';
import path from 'path';

export const runMigrations = async (): Promise<void> => {
  try {
    logger.info('Running database migrations...');

    // Determine which schema to use based on database type
    const dbType = process.env.DB_TYPE || 'sqlite';
    const schemaFile = dbType === 'sqlite' ? 'schema-sqlite.sql' : 'schema.sql';
    const schemaPath = path.join(__dirname, schemaFile);

    if (!fs.existsSync(schemaPath)) {
      throw new Error(`Schema file not found: ${schemaPath}`);
    }

    const schema = fs.readFileSync(schemaPath, 'utf8');

    // For SQLite, we need to execute statements one by one
    if (dbType === 'sqlite') {
      const statements = schema
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      for (const statement of statements) {
        try {
          await db.query(statement + ';');
        } catch (error) {
          // Log but don't fail on already exists errors
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (!errorMessage.includes('already exists')) {
            logger.warn('Migration statement warning:', { statement: statement.substring(0, 100), error: errorMessage });
          }
        }
      }
    } else {
      // PostgreSQL can handle the full schema at once
      await db.query(schema);
    }

    logger.info('Database migrations completed successfully');
  } catch (error) {
    logger.error('Failed to run database migrations:', error);
    throw error;
  }
};

export const createDatabase = async (): Promise<void> => {
  try {
    // This would typically be run separately to create the database
    // For now, we assume the database exists
    logger.info('Database creation skipped - assuming database exists');
  } catch (error) {
    logger.error('Failed to create database:', error);
    throw error;
  }
};