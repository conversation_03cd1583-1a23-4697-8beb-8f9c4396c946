{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:all": "concurrently \"npm run dev --prefix backend\" \"npm run dev\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@types/react-beautiful-dnd": "^13.1.8", "framer-motion": "^11.2.12", "lucide-react": "^0.395.0", "monaco-editor": "^0.52.2", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-native-web": "^0.19.12", "react-resizable-panels": "^3.0.4", "react-router-dom": "^6.23.1", "socket.io-client": "^4.8.1", "sonner": "^1.5.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}