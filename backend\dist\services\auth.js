"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const database_1 = require("@/config/database");
const jwt_1 = require("@/utils/jwt");
const logger_1 = require("@/utils/logger");
class AuthService {
    static async checkDatabaseConnection() {
        try {
            await database_1.db.query('SELECT 1');
            this.databaseAvailable = true;
            return true;
        }
        catch (error) {
            this.databaseAvailable = false;
            logger_1.logger.warn('Database not available, using in-memory auth storage');
            return false;
        }
    }
    static async register(data) {
        const { email, password, name } = data;
        try {
            const dbAvailable = await this.checkDatabaseConnection();
            if (dbAvailable) {
                return await this.registerWithDatabase(data);
            }
            else {
                return await this.registerInMemory(data);
            }
        }
        catch (error) {
            logger_1.logger.error('Registration error:', error);
            throw error;
        }
    }
    static async registerWithDatabase(data) {
        const { email, password, name } = data;
        const existingUser = await database_1.db.query('SELECT id FROM users WHERE email = ?', [email]);
        if (existingUser.rows.length > 0) {
            throw new Error('User with this email already exists');
        }
        const passwordHash = await bcryptjs_1.default.hash(password, this.SALT_ROUNDS);
        const crypto = require('crypto');
        const userId = crypto.randomBytes(16).toString('hex');
        const userQuery = `
      INSERT INTO users (id, email, name, password_hash, preferences)
      VALUES (?, ?, ?, ?, ?)
      RETURNING id, email, name, avatar, subscription_plan, preferences, created_at, updated_at
    `;
        const defaultPreferences = {
            theme: 'light',
            editorSettings: {
                fontSize: 14,
                tabSize: 2,
                wordWrap: true,
                minimap: true
            },
            defaultTemplate: 'basic',
            aiProvider: 'openai'
        };
        const result = await database_1.db.query(userQuery, [userId, email, name, passwordHash, JSON.stringify(defaultPreferences)]);
        const userRow = result.rows[0];
        const user = {
            id: userRow.id,
            email: userRow.email,
            name: userRow.name,
            avatar: userRow.avatar,
            subscriptionPlan: userRow.subscription_plan,
            apiTokens: {},
            preferences: userRow.preferences,
            createdAt: userRow.created_at,
            updatedAt: userRow.updated_at,
        };
        const accessToken = (0, jwt_1.generateAccessToken)({ userId: user.id, email: user.email });
        const refreshToken = (0, jwt_1.generateRefreshToken)({ userId: user.id, email: user.email });
        await this.storeRefreshToken(user.id, refreshToken);
        logger_1.logger.info(`User registered successfully: ${email}`);
        return {
            user: this.sanitizeUser(user),
            accessToken,
            refreshToken,
        };
    }
    static async registerInMemory(data) {
        const { email, password, name } = data;
        if (this.inMemoryUsers.has(email)) {
            throw new Error('User with this email already exists');
        }
        const passwordHash = await bcryptjs_1.default.hash(password, this.SALT_ROUNDS);
        const defaultPreferences = {
            theme: 'light',
            editorSettings: {
                fontSize: 14,
                tabSize: 2,
                wordWrap: true,
                minimap: true
            },
            defaultTemplate: 'basic',
            aiProvider: 'openai'
        };
        const user = {
            id: `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            email,
            name,
            avatar: undefined,
            subscriptionPlan: 'free',
            apiTokens: {},
            preferences: defaultPreferences,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.inMemoryUsers.set(email, { ...user, passwordHash });
        const accessToken = (0, jwt_1.generateAccessToken)({ userId: user.id, email: user.email });
        const refreshToken = (0, jwt_1.generateRefreshToken)({ userId: user.id, email: user.email });
        logger_1.logger.info(`User registered successfully (in-memory): ${email}`);
        return {
            user: this.sanitizeUser(user),
            accessToken,
            refreshToken,
        };
    }
    static async login(data) {
        const { email, password } = data;
        try {
            const dbAvailable = await this.checkDatabaseConnection();
            if (dbAvailable) {
                return await this.loginWithDatabase(data);
            }
            else {
                return await this.loginInMemory(data);
            }
        }
        catch (error) {
            logger_1.logger.error('Login error:', error);
            throw error;
        }
    }
    static async loginWithDatabase(data) {
        const { email, password } = data;
        const userQuery = `
      SELECT id, email, name, avatar, subscription_plan, password_hash, api_tokens, preferences, created_at, updated_at
      FROM users
      WHERE email = ?
    `;
        const result = await database_1.db.query(userQuery, [email]);
        if (result.rows.length === 0) {
            throw new Error('Invalid email or password');
        }
        const userRow = result.rows[0];
        const isValidPassword = await bcryptjs_1.default.compare(password, userRow.password_hash);
        if (!isValidPassword) {
            throw new Error('Invalid email or password');
        }
        const user = {
            id: userRow.id,
            email: userRow.email,
            name: userRow.name,
            avatar: userRow.avatar,
            subscriptionPlan: userRow.subscription_plan,
            apiTokens: userRow.api_tokens || {},
            preferences: userRow.preferences || {},
            createdAt: userRow.created_at,
            updatedAt: userRow.updated_at,
        };
        const accessToken = (0, jwt_1.generateAccessToken)({ userId: user.id, email: user.email });
        const refreshToken = (0, jwt_1.generateRefreshToken)({ userId: user.id, email: user.email });
        await this.storeRefreshToken(user.id, refreshToken);
        logger_1.logger.info(`User logged in successfully: ${email}`);
        return {
            user: this.sanitizeUser(user),
            accessToken,
            refreshToken,
        };
    }
    static async loginInMemory(data) {
        const { email, password } = data;
        const userWithPassword = this.inMemoryUsers.get(email);
        if (!userWithPassword) {
            throw new Error('Invalid email or password');
        }
        const isValidPassword = await bcryptjs_1.default.compare(password, userWithPassword.passwordHash);
        if (!isValidPassword) {
            throw new Error('Invalid email or password');
        }
        const { passwordHash, ...user } = userWithPassword;
        const accessToken = (0, jwt_1.generateAccessToken)({ userId: user.id, email: user.email });
        const refreshToken = (0, jwt_1.generateRefreshToken)({ userId: user.id, email: user.email });
        logger_1.logger.info(`User logged in successfully (in-memory): ${email}`);
        return {
            user: this.sanitizeUser(user),
            accessToken,
            refreshToken,
        };
    }
    static async refreshToken(refreshToken) {
        try {
            const payload = (0, jwt_1.verifyRefreshToken)(refreshToken);
            if (!payload) {
                throw new Error('Invalid refresh token');
            }
            const tokenQuery = `
        SELECT id, user_id, expires_at, revoked_at
        FROM refresh_tokens 
        WHERE token = $1 AND user_id = $2
      `;
            const result = await database_1.db.query(tokenQuery, [refreshToken, payload.userId]);
            if (result.rows.length === 0) {
                throw new Error('Refresh token not found');
            }
            const tokenRow = result.rows[0];
            if (tokenRow.revoked_at) {
                throw new Error('Refresh token has been revoked');
            }
            if (new Date() > new Date(tokenRow.expires_at)) {
                throw new Error('Refresh token has expired');
            }
            const newAccessToken = (0, jwt_1.generateAccessToken)({ userId: payload.userId, email: payload.email });
            const newRefreshToken = (0, jwt_1.generateRefreshToken)({ userId: payload.userId, email: payload.email });
            await database_1.db.query('UPDATE refresh_tokens SET revoked_at = CURRENT_TIMESTAMP WHERE id = ?', [tokenRow.id]);
            await this.storeRefreshToken(payload.userId, newRefreshToken);
            return {
                accessToken: newAccessToken,
                refreshToken: newRefreshToken,
            };
        }
        catch (error) {
            logger_1.logger.error('Token refresh error:', error);
            throw error;
        }
    }
    static async logout(userId, refreshToken) {
        try {
            if (refreshToken) {
                await database_1.db.query('UPDATE refresh_tokens SET revoked_at = CURRENT_TIMESTAMP WHERE token = ? AND user_id = ?', [refreshToken, userId]);
            }
            else {
                await database_1.db.query('UPDATE refresh_tokens SET revoked_at = CURRENT_TIMESTAMP WHERE user_id = ? AND revoked_at IS NULL', [userId]);
            }
            logger_1.logger.info(`User logged out: ${userId}`);
        }
        catch (error) {
            logger_1.logger.error('Logout error:', error);
            throw error;
        }
    }
    static async storeRefreshToken(userId, token) {
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7);
        await database_1.db.query('INSERT INTO refresh_tokens (user_id, token, expires_at) VALUES (?, ?, ?)', [userId, token, expiresAt]);
    }
    static sanitizeUser(user) {
        const { apiTokens, ...sanitizedUser } = user;
        return sanitizedUser;
    }
    static async cleanupExpiredTokens() {
        try {
            const result = await database_1.db.query('DELETE FROM refresh_tokens WHERE expires_at < CURRENT_TIMESTAMP OR revoked_at IS NOT NULL');
            if (result.rowCount && result.rowCount > 0) {
                logger_1.logger.info(`Cleaned up ${result.rowCount} expired/revoked refresh tokens`);
            }
        }
        catch (error) {
            logger_1.logger.error('Token cleanup error:', error);
        }
    }
}
exports.AuthService = AuthService;
AuthService.SALT_ROUNDS = 12;
AuthService.inMemoryUsers = new Map();
AuthService.databaseAvailable = true;
//# sourceMappingURL=auth.js.map