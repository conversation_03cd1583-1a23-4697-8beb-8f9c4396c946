{"name": "sqlite", "version": "5.1.1", "description": "SQLite client for Node.js applications with SQL-based migrations API written in Typescript", "main": "build/index.js", "types": "build/index.d.ts", "type": "commonjs", "exports": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs"}, "scripts": {"build": "npm run build:clean && npm run compile && cp -R src/index.mjs src/vendor-typings build", "build:docs": "typedoc --options typedoc.js --plugin typedoc-plugin-markdown src/ --out docs", "build:clean": "rm -rf build/*", "compile": "tsc", "debug": "ts-node-dev --inspect -- src/index.ts", "debug:break": "ts-node-dev --inspect-brk -- src/index.ts", "test": "jest", "test:ci": "jest --ci --coverage", "test:debug": "node --inspect-brk node_modules/.bin/jest", "test:watch": "jest --watch", "test:coverage:watch": "jest --coverage --watch", "toc": "toc-md README.md README.md", "lint-staged": "lint-staged", "prepare-publish": "npm run changelog:prepare && version-bump && npm run changelog:release && npm run changelog:stamp", "add-readme": "git add README.md", "version-bump": "version-bump", "changelog:help": "changelog-version", "changelog:verify": "changelog-version verify", "changelog:prepare": "changelog-version prepare", "changelog:stamp": "git-commit-stamper parse CHANGELOG.md", "changelog:release": "changelog-version release", "lint": "standard --fix", "ts-node-dev": "ts-node-dev"}, "repository": {"type": "git", "url": "git+https://github.com/kriasoft/node-sqlite.git"}, "author": "Kriasoft <<EMAIL>> (https://www.kriasoft.com)", "contributors": ["<PERSON> <<EMAIL>> (https://twitter.com/koistya)", "<PERSON> <<EMAIL>> (https://theogravity.com)"], "keywords": ["sqlite", "db", "database", "sql", "migrate", "migration", "migrations", "migrator", "seed", "seeds", "seeder", "node", "node5", "babel", "es6", "async", "promise", "promises", "api"], "license": "MIT", "bugs": {"url": "https://github.com/kriasoft/node-sqlite/issues"}, "homepage": "https://github.com/kriasoft/node-sqlite#readme", "devDependencies": {"@theo.gravity/changelog-version": "2.1.11", "@theo.gravity/version-bump": "2.0.14", "@types/jest": "27.4.1", "@types/node": "^17.0.25", "@types/sqlite3": "^3.1.8", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "eslint": "8.14.0", "git-commit-stamper": "^1.0.10", "jest": "27.5.1", "jest-cli": "27.5.1", "jest-junit-reporter": "1.1.0", "lint-staged": "12.4.0", "pre-commit": "1.2.2", "prettier-standard": "^15.0.1", "sql-template-strings": "^2.2.2", "sqlite3": "^5.1.6", "sqlite3-offline-next": "^5.0.3", "standardx": "^7.0.0", "toc-md-alt": "^0.4.6", "ts-jest": "27.1.4", "ts-node": "10.7.0", "ts-node-dev": "1.1.8", "typedoc": "^0.22.15", "typedoc-plugin-markdown": "^3.12.1", "typescript": "4.6.3", "version-bump-plugin-git": "^2.0.1"}, "eslintConfig": {"parserOptions": {"ecmaVersion": 6, "sourceType": "module", "ecmaFeatures": {"modules": true}}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint/eslint-plugin"], "rules": {"@typescript-eslint/no-unused-vars": [2, {"args": "none"}]}}, "lint-staged": {"src/**/*.ts": ["prettier-standard", "git add"]}, "pre-commit": ["toc", "lint-staged", "test:ci", "build"]}