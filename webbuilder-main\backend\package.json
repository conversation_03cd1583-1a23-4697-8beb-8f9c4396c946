{"name": "webbuilder-backend", "version": "1.0.0", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "prestart": "npm run build", "lint": "eslint src --ext .ts", "test": "echo \"Error: no test specified\" && exit 1", "health": "curl http://localhost:3001/health"}, "keywords": ["webbuilder", "react-native", "ai", "backend"], "author": "Webbuilder Team", "license": "MIT", "description": "Backend server for AI-powered React Native web builder with live preview, project management, and real-time collaboration", "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "openai": "^5.11.0", "socket.io": "^4.8.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.1.0", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}