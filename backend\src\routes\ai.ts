import { Router, Response } from 'express';
import { aiService } from '../services/aiService';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { rateLimit } from '../middleware/rateLimiter';
import { logger } from '../utils/logger';
import {
  AuthRequest,
  GenerateCodeRequest,
  GenerateCodeResponse,
  ExplainCodeRequest,
  ExplainCodeResponse,
  FixCodeRequest,
  FixCodeResponse,
  ModifyCodeRequest,
  ModifyCodeResponse,
  ChatRequest,
  ChatResponse,
} from '../types';
import Joi from 'joi';

const router = Router();

// Validation schemas
const generateCodeSchema = Joi.object({
  prompt: Joi.string().required().min(1).max(2000),
  projectId: Joi.string().required().uuid(),
  sessionId: Joi.string().optional().uuid(),
  filePath: Joi.string().optional(),
  language: Joi.string().optional().valid('typescript', 'javascript', 'json'),
  context: Joi.object({
    existingCode: Joi.string().optional(),
    dependencies: Joi.array().items(Joi.string()).optional(),
    targetPlatform: Joi.string().optional().valid('ios', 'android', 'web'),
  }).optional(),
});

const explainCodeSchema = Joi.object({
  code: Joi.string().required().min(1).max(10000),
  language: Joi.string().required().valid('typescript', 'javascript', 'json', 'jsx', 'tsx'),
  filePath: Joi.string().optional(),
  projectId: Joi.string().required().uuid(),
  sessionId: Joi.string().optional().uuid(),
});

const fixCodeSchema = Joi.object({
  code: Joi.string().required().min(1).max(10000),
  error: Joi.string().required().min(1).max(1000),
  language: Joi.string().required().valid('typescript', 'javascript', 'json', 'jsx', 'tsx'),
  filePath: Joi.string().required(),
  projectId: Joi.string().required().uuid(),
  sessionId: Joi.string().optional().uuid(),
});

const modifyCodeSchema = Joi.object({
  code: Joi.string().required().min(1).max(10000),
  instruction: Joi.string().required().min(1).max(1000),
  language: Joi.string().required().valid('typescript', 'javascript', 'json', 'jsx', 'tsx'),
  filePath: Joi.string().required(),
  projectId: Joi.string().required().uuid(),
  sessionId: Joi.string().optional().uuid(),
});

const chatSchema = Joi.object({
  message: Joi.string().required().min(1).max(2000),
  projectId: Joi.string().required().uuid(),
  sessionId: Joi.string().optional().uuid(),
  includeContext: Joi.boolean().optional().default(false),
});

const suggestImprovementsSchema = Joi.object({
  code: Joi.string().required().min(1).max(10000),
  language: Joi.string().required().valid('typescript', 'javascript', 'json', 'jsx', 'tsx'),
  filePath: Joi.string().required(),
});

// Apply middleware to all routes
router.use(authenticateToken);
router.use(rateLimit); // Rate limiting for AI requests

/**
 * @route POST /api/ai/generate
 * @desc Generate React Native code from natural language prompt
 * @access Private
 */
router.post('/generate', 
  validateRequest(generateCodeSchema),
  async (req: AuthRequest, res: Response) => {
    try {
      const request: GenerateCodeRequest = req.body;
      
      logger.info(`Code generation request from user ${req.user?.id}`, {
        projectId: request.projectId,
        prompt: request.prompt.substring(0, 100),
      });

      const generatedCode = await aiService.generateCode(request);
      
      const response: GenerateCodeResponse = {
        success: true,
        generatedCode,
        sessionId: request.sessionId || generatedCode.filePath || 'default',
      };

      logger.info(`Code generation completed for user ${req.user?.id}`, {
        projectId: request.projectId,
        codeLength: generatedCode.code.length,
        confidence: generatedCode.confidence,
      });

      res.json(response);
    } catch (error) {
      logger.error('Error in code generation:', error);
      
      const response: GenerateCodeResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate code',
        sessionId: req.body.sessionId || 'default',
      };
      
      res.status(500).json(response);
    }
  }
);

/**
 * @route POST /api/ai/explain
 * @desc Explain provided code
 * @access Private
 */
router.post('/explain',
  validateRequest(explainCodeSchema),
  async (req: AuthRequest, res: Response) => {
    try {
      const request: ExplainCodeRequest = req.body;
      
      logger.info(`Code explanation request from user ${req.user?.id}`, {
        projectId: request.projectId,
        language: request.language,
        codeLength: request.code.length,
      });

      const explanation = await aiService.explainCode(request);
      
      const response: ExplainCodeResponse = {
        success: true,
        explanation,
        sessionId: request.sessionId || 'default',
      };

      res.json(response);
    } catch (error) {
      logger.error('Error in code explanation:', error);
      
      const response: ExplainCodeResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to explain code',
        sessionId: req.body.sessionId || 'default',
      };
      
      res.status(500).json(response);
    }
  }
);

/**
 * @route POST /api/ai/fix
 * @desc Fix code based on error message
 * @access Private
 */
router.post('/fix',
  validateRequest(fixCodeSchema),
  async (req: AuthRequest, res: Response) => {
    try {
      const request: FixCodeRequest = req.body;
      
      logger.info(`Code fix request from user ${req.user?.id}`, {
        projectId: request.projectId,
        filePath: request.filePath,
        error: request.error.substring(0, 100),
      });

      const codeFix = await aiService.fixCode(request);
      
      const response: FixCodeResponse = {
        success: true,
        codeFix,
        sessionId: request.sessionId || 'default',
      };

      res.json(response);
    } catch (error) {
      logger.error('Error in code fixing:', error);
      
      const response: FixCodeResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fix code',
        sessionId: req.body.sessionId || 'default',
      };
      
      res.status(500).json(response);
    }
  }
);

/**
 * @route POST /api/ai/modify
 * @desc Modify code based on instruction
 * @access Private
 */
router.post('/modify',
  validateRequest(modifyCodeSchema),
  async (req: AuthRequest, res: Response) => {
    try {
      const request: ModifyCodeRequest = req.body;
      
      logger.info(`Code modification request from user ${req.user?.id}`, {
        projectId: request.projectId,
        filePath: request.filePath,
        instruction: request.instruction.substring(0, 100),
      });

      const codeDiff = await aiService.modifyCode(request);
      
      const response: ModifyCodeResponse = {
        success: true,
        codeDiff,
        sessionId: request.sessionId || 'default',
      };

      res.json(response);
    } catch (error) {
      logger.error('Error in code modification:', error);
      
      const response: ModifyCodeResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to modify code',
        sessionId: req.body.sessionId || 'default',
      };
      
      res.status(500).json(response);
    }
  }
);

/**
 * @route POST /api/ai/chat
 * @desc Chat with AI assistant about the project
 * @access Private
 */
router.post('/chat',
  validateRequest(chatSchema),
  async (req: AuthRequest, res: Response) => {
    try {
      const request: ChatRequest = req.body;
      
      logger.info(`AI chat request from user ${req.user?.id}`, {
        projectId: request.projectId,
        message: request.message.substring(0, 100),
        includeContext: request.includeContext,
      });

      const chatResponse = await aiService.chat(request);
      
      const response: ChatResponse = {
        success: true,
        response: chatResponse,
        sessionId: request.sessionId || 'default',
      };

      res.json(response);
    } catch (error) {
      logger.error('Error in AI chat:', error);
      
      const response: ChatResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process chat',
        sessionId: req.body.sessionId || 'default',
      };
      
      res.status(500).json(response);
    }
  }
);

/**
 * @route POST /api/ai/suggest
 * @desc Get code improvement suggestions
 * @access Private
 */
router.post('/suggest',
  validateRequest(suggestImprovementsSchema),
  async (req: AuthRequest, res: Response) => {
    try {
      const { code, language, filePath } = req.body;
      
      logger.info(`Code suggestions request from user ${req.user?.id}`, {
        language,
        filePath,
        codeLength: code.length,
      });

      const suggestions = await aiService.suggestImprovements(code, language, filePath);
      
      res.json({
        success: true,
        suggestions,
      });
    } catch (error) {
      logger.error('Error in code suggestions:', error);
      
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get suggestions',
        suggestions: [],
      });
    }
  }
);

/**
 * @route GET /api/ai/session/:sessionId/history
 * @desc Get conversation history for a session
 * @access Private
 */
router.get('/session/:sessionId/history',
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const { sessionId } = req.params;
      
      if (!sessionId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(sessionId)) {
        res.status(400).json({
          success: false,
          error: 'Invalid session ID format',
        });
        return;
      }

      const history = await aiService.getSessionHistory(sessionId);

      res.json({
        success: true,
        history,
      });
    } catch (error) {
      logger.error('Error getting session history:', error);

      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get session history',
      });
    }
  }
);

/**
 * @route DELETE /api/ai/session/:sessionId
 * @desc Clear a conversation session
 * @access Private
 */
router.delete('/session/:sessionId',
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const { sessionId } = req.params;

      if (!sessionId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(sessionId)) {
        res.status(400).json({
          success: false,
          error: 'Invalid session ID format',
        });
        return;
      }

      await aiService.clearSession(sessionId);

      res.json({
        success: true,
        message: 'Session cleared successfully',
      });
    } catch (error) {
      logger.error('Error clearing session:', error);

      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to clear session',
      });
    }
  }
);

/**
 * @route GET /api/ai/health
 * @desc Check AI service health
 * @access Private
 */
router.get('/health',
  async (req: AuthRequest, res: Response) => {
    try {
const health = await aiService.healthCheck();
      
      res.json({
        success: true,
        providers: health,
        status: Object.values(health).some(Boolean) ? 'healthy' : 'no_providers',
      });
    } catch (error) {
      logger.error('Error checking AI service health:', error);
      
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Health check failed',
      });
    }
  }
);

export default router;