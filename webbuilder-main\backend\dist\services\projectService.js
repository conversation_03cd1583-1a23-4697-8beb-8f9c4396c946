"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectService = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
class ProjectService {
    constructor() {
        // Create projects directory in backend folder
        this.projectsDir = path_1.default.join(__dirname, '../../projects');
        this.ensureProjectsDirectory();
    }
    async ensureProjectsDirectory() {
        try {
            await promises_1.default.mkdir(this.projectsDir, { recursive: true });
        }
        catch (error) {
            console.warn('Could not create projects directory:', error);
        }
    }
    /**
     * Get all projects
     */
    async getAllProjects() {
        try {
            const entries = await promises_1.default.readdir(this.projectsDir, { withFileTypes: true });
            const projects = [];
            for (const entry of entries) {
                if (entry.isDirectory()) {
                    try {
                        const projectPath = path_1.default.join(this.projectsDir, entry.name);
                        const metadataPath = path_1.default.join(projectPath, 'project.json');
                        const metadataExists = await promises_1.default.access(metadataPath).then(() => true).catch(() => false);
                        if (metadataExists) {
                            const metadata = JSON.parse(await promises_1.default.readFile(metadataPath, 'utf-8'));
                            projects.push(metadata);
                        }
                    }
                    catch (error) {
                        console.warn(`Error reading project ${entry.name}:`, error);
                    }
                }
            }
            return projects.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        }
        catch (error) {
            console.error('Error getting all projects:', error);
            return [];
        }
    }
    /**
     * Get a specific project
     */
    async getProject(projectId) {
        try {
            const projectPath = path_1.default.join(this.projectsDir, projectId);
            const metadataPath = path_1.default.join(projectPath, 'project.json');
            const metadata = JSON.parse(await promises_1.default.readFile(metadataPath, 'utf-8'));
            // Add file list
            const files = await this.getProjectFiles(projectId);
            metadata.files = files.map(f => f.path);
            return metadata;
        }
        catch (error) {
            console.warn(`Project ${projectId} not found:`, error);
            return null;
        }
    }
    /**
     * Create a new project
     */
    async createProject(options) {
        const projectId = this.generateProjectId();
        const projectPath = path_1.default.join(this.projectsDir, projectId);
        const project = {
            id: projectId,
            name: options.name,
            description: options.description,
            type: options.type,
            template: options.template,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        try {
            // Create project directory
            await promises_1.default.mkdir(projectPath, { recursive: true });
            // Save project metadata
            await promises_1.default.writeFile(path_1.default.join(projectPath, 'project.json'), JSON.stringify(project, null, 2));
            // Create default project structure based on template
            await this.createProjectStructure(projectPath, options.template || 'basic', options.type);
            return projectId;
        }
        catch (error) {
            console.error('Error creating project:', error);
            throw new Error('Failed to create project');
        }
    }
    /**
     * Update project metadata
     */
    async updateProject(projectId, updates) {
        const project = await this.getProject(projectId);
        if (!project) {
            throw new Error('Project not found');
        }
        const updatedProject = {
            ...project,
            ...updates,
            id: projectId, // Don't allow ID changes
            updatedAt: new Date()
        };
        const projectPath = path_1.default.join(this.projectsDir, projectId);
        await promises_1.default.writeFile(path_1.default.join(projectPath, 'project.json'), JSON.stringify(updatedProject, null, 2));
    }
    /**
     * Delete a project
     */
    async deleteProject(projectId) {
        const projectPath = path_1.default.join(this.projectsDir, projectId);
        try {
            await promises_1.default.rm(projectPath, { recursive: true, force: true });
        }
        catch (error) {
            console.error(`Error deleting project ${projectId}:`, error);
            throw new Error('Failed to delete project');
        }
    }
    /**
     * Get project files
     */
    async getProjectFiles(projectId) {
        const projectPath = path_1.default.join(this.projectsDir, projectId);
        const files = [];
        try {
            await this.walkDirectory(projectPath, projectPath, files);
            return files.filter(f => f.path !== 'project.json'); // Exclude metadata file
        }
        catch (error) {
            console.warn(`Error getting files for project ${projectId}:`, error);
            return [];
        }
    }
    /**
     * Get file content
     */
    async getFileContent(projectId, filePath) {
        const fullPath = path_1.default.join(this.projectsDir, projectId, filePath);
        try {
            return await promises_1.default.readFile(fullPath, 'utf-8');
        }
        catch (error) {
            throw new Error(`File not found: ${filePath}`);
        }
    }
    /**
     * Save file content
     */
    async saveFileContent(projectId, filePath, content) {
        const fullPath = path_1.default.join(this.projectsDir, projectId, filePath);
        try {
            // Ensure directory exists
            await promises_1.default.mkdir(path_1.default.dirname(fullPath), { recursive: true });
            // Write file
            await promises_1.default.writeFile(fullPath, content, 'utf-8');
            // Update project timestamp
            await this.updateProject(projectId, { updatedAt: new Date() });
        }
        catch (error) {
            console.error(`Error saving file ${filePath}:`, error);
            throw new Error(`Failed to save file: ${filePath}`);
        }
    }
    /**
     * Create a new file
     */
    async createFile(projectId, filePath, content = '') {
        const fullPath = path_1.default.join(this.projectsDir, projectId, filePath);
        try {
            // Check if file already exists
            const exists = await promises_1.default.access(fullPath).then(() => true).catch(() => false);
            if (exists) {
                throw new Error(`File already exists: ${filePath}`);
            }
            // Ensure directory exists
            await promises_1.default.mkdir(path_1.default.dirname(fullPath), { recursive: true });
            // Create file
            await promises_1.default.writeFile(fullPath, content, 'utf-8');
            // Update project timestamp
            await this.updateProject(projectId, { updatedAt: new Date() });
        }
        catch (error) {
            console.error(`Error creating file ${filePath}:`, error);
            throw error;
        }
    }
    /**
     * Delete a file
     */
    async deleteFile(projectId, filePath) {
        const fullPath = path_1.default.join(this.projectsDir, projectId, filePath);
        try {
            await promises_1.default.unlink(fullPath);
            // Update project timestamp
            await this.updateProject(projectId, { updatedAt: new Date() });
        }
        catch (error) {
            console.error(`Error deleting file ${filePath}:`, error);
            throw new Error(`Failed to delete file: ${filePath}`);
        }
    }
    // Private helper methods
    generateProjectId() {
        return `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async walkDirectory(dir, basePath, files) {
        const entries = await promises_1.default.readdir(dir, { withFileTypes: true });
        for (const entry of entries) {
            const fullPath = path_1.default.join(dir, entry.name);
            const relativePath = path_1.default.relative(basePath, fullPath);
            if (entry.isDirectory()) {
                files.push({
                    path: relativePath,
                    type: 'directory'
                });
                await this.walkDirectory(fullPath, basePath, files);
            }
            else {
                const stats = await promises_1.default.stat(fullPath);
                files.push({
                    path: relativePath,
                    type: 'file',
                    size: stats.size,
                    lastModified: stats.mtime
                });
            }
        }
    }
    async createProjectStructure(projectPath, template, type) {
        // Create basic React Native project structure
        const directories = [
            'src',
            'src/components',
            'src/screens',
            'src/services',
            'src/utils'
        ];
        for (const dir of directories) {
            await promises_1.default.mkdir(path_1.default.join(projectPath, dir), { recursive: true });
        }
        // Create App.tsx
        const appContent = `import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';

const App: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Welcome to Your App!</Text>
        <Text style={styles.subtitle}>Start building something amazing.</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default App;`;
        await promises_1.default.writeFile(path_1.default.join(projectPath, 'App.tsx'), appContent);
        // Create package.json
        const packageJson = {
            name: `mobile-app-${Date.now()}`,
            version: '1.0.0',
            main: 'App.tsx',
            dependencies: {
                'react': '^18.2.0',
                'react-native': '^0.72.0'
            },
            devDependencies: {
                '@types/react': '^18.2.0',
                '@types/react-native': '^0.72.0',
                'typescript': '^5.0.0'
            }
        };
        await promises_1.default.writeFile(path_1.default.join(projectPath, 'package.json'), JSON.stringify(packageJson, null, 2));
        // Create example component
        const componentContent = `import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface ExampleComponentProps {
  title: string;
}

const ExampleComponent: React.FC<ExampleComponentProps> = ({ title }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>{title}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    margin: 8,
  },
  text: {
    fontSize: 16,
    color: '#333',
  },
});

export default ExampleComponent;`;
        await promises_1.default.writeFile(path_1.default.join(projectPath, 'src/components/ExampleComponent.tsx'), componentContent);
    }
}
exports.ProjectService = ProjectService;
