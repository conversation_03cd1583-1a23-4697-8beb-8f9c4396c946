// Quick server test
const { spawn } = require('child_process');

console.log('🧪 Testing server startup...');

const server = spawn('node', ['dist/server.js'], {
  cwd: __dirname,
  stdio: 'pipe'
});

let output = '';
let hasStarted = false;

server.stdout.on('data', (data) => {
  const text = data.toString();
  output += text;
  console.log(text.trim());
  
  if (text.includes('Webbuilder Backend Server Started!')) {
    hasStarted = true;
    console.log('✅ Server started successfully!');
    server.kill();
  }
});

server.stderr.on('data', (data) => {
  const text = data.toString();
  console.error('❌ Error:', text.trim());
});

server.on('close', (code) => {
  if (hasStarted) {
    console.log('✅ Server test completed successfully!');
    process.exit(0);
  } else {
    console.error('❌ Server failed to start properly');
    process.exit(1);
  }
});

// Timeout after 10 seconds
setTimeout(() => {
  if (!hasStarted) {
    console.log('⏰ Test timeout - killing server');
    server.kill();
  }
}, 10000);
