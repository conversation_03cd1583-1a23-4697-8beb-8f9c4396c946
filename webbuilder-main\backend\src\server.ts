import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';

// Import routes
import aiRoutes from './routes/ai';
import projectRoutes from './routes/projects';
import bundlerRoutes from './routes/bundler';

// Import services
import { ProjectService } from './services/projectService';
import { BundlerService } from './services/bundlerService';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Initialize services
const projectService = new ProjectService();
const bundlerService = new BundlerService();

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Routes
app.use('/api/ai', aiRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/bundler', bundlerRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});

// WebSocket handling
const activeProjects = new Map<string, Set<string>>();

io.on('connection', (socket) => {
  console.log(`Client connected: ${socket.id}`);

  // Join project room
  socket.on('join-project', (projectId: string) => {
    socket.join(projectId);
    
    if (!activeProjects.has(projectId)) {
      activeProjects.set(projectId, new Set());
    }
    activeProjects.get(projectId)?.add(socket.id);
    
    console.log(`Client ${socket.id} joined project ${projectId}`);
    
    // Notify other clients in the project
    socket.to(projectId).emit('user-joined', {
      userId: socket.id,
      timestamp: new Date().toISOString()
    });
  });

  // Handle code changes
  socket.on('code-change', (data: {
    projectId: string;
    filePath: string;
    content: string;
    userId: string;
  }) => {
    console.log(`Code change in project ${data.projectId}: ${data.filePath}`);
    
    // Broadcast to all clients in the project except sender
    socket.to(data.projectId).emit('code-change', {
      ...data,
      userId: socket.id,
      timestamp: new Date().toISOString()
    });

    // TODO: Save file content to storage
    // TODO: Trigger bundler rebuild
  });

  // Handle file save
  socket.on('file-save', async (data: {
    projectId: string;
    filePath: string;
    content: string;
  }) => {
    console.log(`File saved in project ${data.projectId}: ${data.filePath}`);
    
    try {
      // Persist to storage
      await projectService.saveFileContent(data.projectId, data.filePath, data.content);
      
      // Validate code
      const validation = await bundlerService.validateCode({
        projectId: data.projectId,
        filePath: data.filePath,
        content: data.content
      });
      
      // Trigger bundle rebuild for web platform
      const bundleResult = await bundlerService.createBundle({
        projectId: data.projectId,
        platform: 'web'
      });
      
      // Notify all clients in the project
      io.to(data.projectId).emit('file-saved', {
        ...data,
        userId: socket.id,
        timestamp: new Date().toISOString(),
        validation: validation.validation,
        bundleReady: bundleResult.success
      });
      
      console.log(`✅ File ${data.filePath} saved and validated successfully`);
    } catch (error) {
      console.error(`❌ Error saving file ${data.filePath}:`, error);
      
      socket.emit('file-save-error', {
        filePath: data.filePath,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    console.log(`Client disconnected: ${socket.id}`);
    
    // Remove from all projects
    for (const [projectId, clients] of activeProjects.entries()) {
      if (clients.has(socket.id)) {
        clients.delete(socket.id);
        
        // Notify other clients
        socket.to(projectId).emit('user-left', {
          userId: socket.id,
          timestamp: new Date().toISOString()
        });
        
        // Clean up empty projects
        if (clients.size === 0) {
          activeProjects.delete(projectId);
        }
      }
    }
  });

  // Send initial connection success
  socket.emit('connected', {
    socketId: socket.id,
    timestamp: new Date().toISOString()
  });
});

// Error handling
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message,
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// Start server
server.listen(PORT, () => {
  console.log('🚀 Webbuilder Backend Server Started!');
  console.log(`📡 HTTP Server: http://localhost:${PORT}`);
  console.log(`🔌 WebSocket Server: ws://localhost:${PORT}`);
  console.log(`💾 Memory: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);
  console.log(`⏱️  Started at: ${new Date().toISOString()}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

export { io, activeProjects };
