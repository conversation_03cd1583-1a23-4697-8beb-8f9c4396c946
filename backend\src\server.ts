import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { runMigrations } from '@/database/migrations';
import { initializeDatabase, pool } from '@/config/database';
import { WebSocketService } from '@/services/websocket';
import { AuthService } from '@/services/auth';
import { logger } from '@/utils/logger';
import { errorHandler, notFoundHandler } from '@/middleware/errorHandler';
import { apiRateLimit } from '@/middleware/rateLimiter';

// Import routes
import authRoutes from '@/routes/auth';
import healthRoutes from '@/routes/health';
import aiRoutes from '@/routes/ai';
import { createProjectsRouter } from '@/routes/projects';
import { createBundlerRouter } from '@/routes/bundler';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const PORT = process.env.PORT || 3001;

// Initialize WebSocket service
const wsService = new WebSocketService(server);

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('combined', {
  stream: {
    write: (message: string) => {
      logger.info(message.trim());
    }
  }
}));

// Rate limiting
app.use('/api/', apiRateLimit);

// Health check routes (before rate limiting)
app.use('/health', healthRoutes);

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/projects', createProjectsRouter(pool));
app.use('/api/bundler', createBundlerRouter(pool, wsService));

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Mobile App Builder Platform API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      auth: '/api/auth',
      ai: '/api/ai',
      projects: '/api/projects',
      bundler: '/api/bundler',
      docs: '/api/docs' // Future API documentation
    }
  });
});

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Graceful shutdown handler
const gracefulShutdown = (signal: string) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);
  
  server.close(() => {
    logger.info('HTTP server closed');
    
    // Close database connections
    // pool.end() would be called here if we exposed it
    
    logger.info('Graceful shutdown completed');
    process.exit(0);
  });

  // Force shutdown after 30 seconds
  setTimeout(() => {
    logger.error('Forced shutdown after timeout');
    process.exit(1);
  }, 30000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start server
const startServer = async (): Promise<void> => {
  try {
    // Initialize database (optional in development)
    logger.info('Initializing database connection...');
    let databaseAvailable = false;

    try {
      await initializeDatabase();

      // Run migrations
      logger.info('Running database migrations...');
      await runMigrations();
      logger.info('Database initialized successfully');
      databaseAvailable = true;
    } catch (dbError) {
      logger.warn('Database connection failed. Continuing without database...', {
        error: dbError instanceof Error ? dbError.message : 'Unknown error',
        mode: process.env.NODE_ENV || 'development'
      });
      logger.warn('Features requiring database (auth, user management) will use in-memory fallbacks.');

      // In production, you might want to exit here
      if (process.env.NODE_ENV === 'production' && process.env.REQUIRE_DATABASE === 'true') {
        throw dbError;
      }
    }
    
    // Start cleanup job for expired tokens
    setInterval(() => {
      AuthService.cleanupExpiredTokens();
    }, 60 * 60 * 1000); // Run every hour
    
    // Start server
    server.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`WebSocket server initialized`);
      logger.info(`Health check available at: http://localhost:${PORT}/health`);
    });
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();

export { app, server, wsService };