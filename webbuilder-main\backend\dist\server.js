"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.activeProjects = exports.io = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const dotenv_1 = __importDefault(require("dotenv"));
// Import routes
const ai_1 = __importDefault(require("./routes/ai"));
const projects_1 = __importDefault(require("./routes/projects"));
const bundler_1 = __importDefault(require("./routes/bundler"));
// Import services
const projectService_1 = require("./services/projectService");
const bundlerService_1 = require("./services/bundlerService");
// Load environment variables
dotenv_1.default.config();
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});
exports.io = io;
const PORT = process.env.PORT || 3001;
// Initialize services
const projectService = new projectService_1.ProjectService();
const bundlerService = new bundlerService_1.BundlerService();
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
// Request logging
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});
// Routes
app.use('/api/ai', ai_1.default);
app.use('/api/projects', projects_1.default);
app.use('/api/bundler', bundler_1.default);
// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage()
    });
});
// WebSocket handling
const activeProjects = new Map();
exports.activeProjects = activeProjects;
io.on('connection', (socket) => {
    console.log(`Client connected: ${socket.id}`);
    // Join project room
    socket.on('join-project', (projectId) => {
        socket.join(projectId);
        if (!activeProjects.has(projectId)) {
            activeProjects.set(projectId, new Set());
        }
        activeProjects.get(projectId)?.add(socket.id);
        console.log(`Client ${socket.id} joined project ${projectId}`);
        // Notify other clients in the project
        socket.to(projectId).emit('user-joined', {
            userId: socket.id,
            timestamp: new Date().toISOString()
        });
    });
    // Handle code changes
    socket.on('code-change', (data) => {
        console.log(`Code change in project ${data.projectId}: ${data.filePath}`);
        // Broadcast to all clients in the project except sender
        socket.to(data.projectId).emit('code-change', {
            ...data,
            userId: socket.id,
            timestamp: new Date().toISOString()
        });
        // TODO: Save file content to storage
        // TODO: Trigger bundler rebuild
    });
    // Handle file save
    socket.on('file-save', async (data) => {
        console.log(`File saved in project ${data.projectId}: ${data.filePath}`);
        try {
            // Persist to storage
            await projectService.saveFileContent(data.projectId, data.filePath, data.content);
            // Validate code
            const validation = await bundlerService.validateCode({
                projectId: data.projectId,
                filePath: data.filePath,
                content: data.content
            });
            // Trigger bundle rebuild for web platform
            const bundleResult = await bundlerService.createBundle({
                projectId: data.projectId,
                platform: 'web'
            });
            // Notify all clients in the project
            io.to(data.projectId).emit('file-saved', {
                ...data,
                userId: socket.id,
                timestamp: new Date().toISOString(),
                validation: validation.validation,
                bundleReady: bundleResult.success
            });
            console.log(`✅ File ${data.filePath} saved and validated successfully`);
        }
        catch (error) {
            console.error(`❌ Error saving file ${data.filePath}:`, error);
            socket.emit('file-save-error', {
                filePath: data.filePath,
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            });
        }
    });
    // Handle disconnect
    socket.on('disconnect', () => {
        console.log(`Client disconnected: ${socket.id}`);
        // Remove from all projects
        for (const [projectId, clients] of activeProjects.entries()) {
            if (clients.has(socket.id)) {
                clients.delete(socket.id);
                // Notify other clients
                socket.to(projectId).emit('user-left', {
                    userId: socket.id,
                    timestamp: new Date().toISOString()
                });
                // Clean up empty projects
                if (clients.size === 0) {
                    activeProjects.delete(projectId);
                }
            }
        }
    });
    // Send initial connection success
    socket.emit('connected', {
        socketId: socket.id,
        timestamp: new Date().toISOString()
    });
});
// Error handling
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({
        error: 'Internal server error',
        message: err.message,
        timestamp: new Date().toISOString()
    });
});
// 404 handler
app.use((req, res) => {
    res.status(404).json({
        error: 'Route not found',
        path: req.originalUrl,
        timestamp: new Date().toISOString()
    });
});
// Start server
server.listen(PORT, () => {
    console.log('🚀 Webbuilder Backend Server Started!');
    console.log(`📡 HTTP Server: http://localhost:${PORT}`);
    console.log(`🔌 WebSocket Server: ws://localhost:${PORT}`);
    console.log(`💾 Memory: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);
    console.log(`⏱️  Started at: ${new Date().toISOString()}`);
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Process terminated');
    });
});
