"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDatabase = exports.runMigrations = void 0;
const database_1 = require("@/config/database");
const logger_1 = require("@/utils/logger");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const runMigrations = async () => {
    try {
        logger_1.logger.info('Running database migrations...');
        const dbType = process.env.DB_TYPE || 'sqlite';
        const schemaFile = dbType === 'sqlite' ? 'schema-sqlite.sql' : 'schema.sql';
        const schemaPath = path_1.default.join(__dirname, schemaFile);
        if (!fs_1.default.existsSync(schemaPath)) {
            throw new Error(`Schema file not found: ${schemaPath}`);
        }
        const schema = fs_1.default.readFileSync(schemaPath, 'utf8');
        if (dbType === 'sqlite') {
            const statements = schema
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0);
            for (const statement of statements) {
                try {
                    await database_1.db.query(statement + ';');
                }
                catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    if (!errorMessage.includes('already exists')) {
                        logger_1.logger.warn('Migration statement warning:', { statement: statement.substring(0, 100), error: errorMessage });
                    }
                }
            }
        }
        else {
            await database_1.db.query(schema);
        }
        logger_1.logger.info('Database migrations completed successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to run database migrations:', error);
        throw error;
    }
};
exports.runMigrations = runMigrations;
const createDatabase = async () => {
    try {
        logger_1.logger.info('Database creation skipped - assuming database exists');
    }
    catch (error) {
        logger_1.logger.error('Failed to create database:', error);
        throw error;
    }
};
exports.createDatabase = createDatabase;
//# sourceMappingURL=migrations.js.map