{"version": 3, "file": "opt-arg.js", "sourceRoot": "", "sources": ["../../src/opt-arg.ts"], "names": [], "mappings": "AAGA,MAAM,WAAW,GAAG,CAAC,GAAQ,EAAE,CAAS,EAAE,EAAE,CAC1C,OAAO,GAAG,KAAK,WAAW,IAAI,OAAO,GAAG,KAAK,CAAC,CAAA;AAEhD,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,CAAM,EAAsB,EAAE,CAC5D,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,KAAK,QAAQ;IACrB,WAAW,CAAC,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC;IACtC,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC;IAC5B,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC;IACnC,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC;IACnC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC;IAChC,WAAW,CAAC,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC;IACnC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;IAC1E,WAAW,CAAC,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;AAEnC,MAAM,CAAC,MAAM,mBAAmB,GAAqB,CACnD,CAAM,EACsB,EAAE;IAC9B,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;IAC3C,CAAC;AACH,CAAC,CAAA;AAsBD,MAAM,OAAO,GAAG,CACd,GAAM,EAKsB,EAAE;IAC9B,mBAAmB,CAAC,GAAG,CAAC,CAAA;IACxB,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,GAAG,GAAG,CAAA;IAChC,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,OAAkC,CAAA;IAC3C,CAAC;IACD,MAAM,OAAO,GACX,IAAI,KAAK,IAAI,CAAC,CAAC;QACb,GAAG,CAAC,MAAM,CAAC,CAAC;YACV,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE;YACxB,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACZ;gBACE,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,GAAG,IAAI;aACR;YACH,CAAC,CAAC,IAAI,CAAA;IACR,OAAO;QACL,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,GAAG,OAAO;YACV,iDAAiD;YACjD,6CAA6C;YAC7C,QAAQ,EAAE,IAAI;YACd,aAAa,EAAE,KAAK;SACrB;KACsD,CAAA;AAC3D,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,MAA0B,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;AACpE,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,MAAyB,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA", "sourcesContent": ["import { Dirent, Stats } from 'fs'\nimport { GlobOptions } from 'glob'\n\nconst typeOrUndef = (val: any, t: string) =>\n  typeof val === 'undefined' || typeof val === t\n\nexport const isRimrafOptions = (o: any): o is RimrafOptions =>\n  !!o &&\n  typeof o === 'object' &&\n  typeOrUndef(o.preserveRoot, 'boolean') &&\n  typeOrUndef(o.tmp, 'string') &&\n  typeOrUndef(o.maxRetries, 'number') &&\n  typeOrUndef(o.retryDelay, 'number') &&\n  typeOrUndef(o.backoff, 'number') &&\n  typeOrUndef(o.maxBackoff, 'number') &&\n  (typeOrUndef(o.glob, 'boolean') || (o.glob && typeof o.glob === 'object')) &&\n  typeOrUndef(o.filter, 'function')\n\nexport const assertRimrafOptions: (o: any) => void = (\n  o: any,\n): asserts o is RimrafOptions => {\n  if (!isRimrafOptions(o)) {\n    throw new Error('invalid rimraf options')\n  }\n}\n\nexport interface RimrafAsyncOptions {\n  preserveRoot?: boolean\n  tmp?: string\n  maxRetries?: number\n  retryDelay?: number\n  backoff?: number\n  maxBackoff?: number\n  signal?: AbortSignal\n  glob?: boolean | GlobOptions\n  filter?:\n    | ((path: string, ent: Dirent | Stats) => boolean)\n    | ((path: string, ent: Dirent | Stats) => Promise<boolean>)\n}\n\nexport interface RimrafSyncOptions extends RimrafAsyncOptions {\n  filter?: (path: string, ent: Dirent | Stats) => boolean\n}\n\nexport type RimrafOptions = RimrafSyncOptions | RimrafAsyncOptions\n\nconst optArgT = <T extends RimrafOptions>(\n  opt: T,\n):\n  | (T & {\n      glob: GlobOptions & { withFileTypes: false }\n    })\n  | (T & { glob: undefined }) => {\n  assertRimrafOptions(opt)\n  const { glob, ...options } = opt\n  if (!glob) {\n    return options as T & { glob: undefined }\n  }\n  const globOpt =\n    glob === true ?\n      opt.signal ?\n        { signal: opt.signal }\n      : {}\n    : opt.signal ?\n      {\n        signal: opt.signal,\n        ...glob,\n      }\n    : glob\n  return {\n    ...options,\n    glob: {\n      ...globOpt,\n      // always get absolute paths from glob, to ensure\n      // that we are referencing the correct thing.\n      absolute: true,\n      withFileTypes: false,\n    },\n  } as T & { glob: GlobOptions & { withFileTypes: false } }\n}\n\nexport const optArg = (opt: RimrafAsyncOptions = {}) => optArgT(opt)\nexport const optArgSync = (opt: RimrafSyncOptions = {}) => optArgT(opt)\n"]}