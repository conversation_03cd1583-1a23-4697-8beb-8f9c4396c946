"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const aiService_1 = require("../services/aiService");
const router = express_1.default.Router();
const aiService = new aiService_1.AIService();
/**
 * Generate code from natural language prompt
 */
router.post('/generate', async (req, res) => {
    try {
        const { prompt, projectId, sessionId, filePath, language, context } = req.body;
        if (!prompt || !projectId) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: prompt, projectId'
            });
        }
        console.log(`AI Generate Request: ${prompt.substring(0, 50)}...`);
        const result = await aiService.generateCode({
            prompt,
            projectId,
            sessionId,
            filePath,
            language,
            context
        });
        res.json(result);
    }
    catch (error) {
        console.error('AI Generate Error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            sessionId: req.body.sessionId || 'unknown'
        });
    }
});
/**
 * Chat with AI assistant
 */
router.post('/chat', async (req, res) => {
    try {
        const { message, projectId, sessionId, includeContext } = req.body;
        if (!message || !projectId) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: message, projectId'
            });
        }
        console.log(`AI Chat Request: ${message.substring(0, 50)}...`);
        const result = await aiService.chat({
            message,
            projectId,
            sessionId,
            includeContext
        });
        res.json(result);
    }
    catch (error) {
        console.error('AI Chat Error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            sessionId: req.body.sessionId || 'unknown'
        });
    }
});
/**
 * Explain code functionality
 */
router.post('/explain', async (req, res) => {
    try {
        const { code, language, filePath, projectId, sessionId } = req.body;
        if (!code || !language || !projectId) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: code, language, projectId'
            });
        }
        console.log(`AI Explain Request for ${language} code`);
        const result = await aiService.explainCode({
            code,
            language,
            filePath,
            projectId,
            sessionId
        });
        res.json(result);
    }
    catch (error) {
        console.error('AI Explain Error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            sessionId: req.body.sessionId || 'unknown'
        });
    }
});
/**
 * Fix code based on error message
 */
router.post('/fix', async (req, res) => {
    try {
        const { code, error, language, filePath, projectId, sessionId } = req.body;
        if (!code || !error || !language || !filePath || !projectId) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: code, error, language, filePath, projectId'
            });
        }
        console.log(`AI Fix Request for ${language} code with error: ${error.substring(0, 50)}...`);
        const result = await aiService.fixCode({
            code,
            error,
            language,
            filePath,
            projectId,
            sessionId
        });
        res.json(result);
    }
    catch (error) {
        console.error('AI Fix Error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            sessionId: req.body.sessionId || 'unknown'
        });
    }
});
/**
 * Get code improvement suggestions
 */
router.post('/suggest', async (req, res) => {
    try {
        const { code, language, filePath } = req.body;
        if (!code || !language) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: code, language'
            });
        }
        console.log(`AI Suggestions Request for ${language} code`);
        const result = await aiService.getSuggestions(code, language, filePath);
        res.json(result);
    }
    catch (error) {
        console.error('AI Suggestions Error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * Get AI service health status
 */
router.get('/health', async (req, res) => {
    try {
        const health = await aiService.getHealth();
        res.json(health);
    }
    catch (error) {
        console.error('AI Health Check Error:', error);
        res.status(500).json({
            success: false,
            status: 'unhealthy',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * Get conversation history for a session
 */
router.get('/session/:sessionId/history', async (req, res) => {
    try {
        const { sessionId } = req.params;
        if (!sessionId) {
            return res.status(400).json({
                success: false,
                error: 'Missing sessionId parameter'
            });
        }
        const result = await aiService.getSessionHistory(sessionId);
        res.json(result);
    }
    catch (error) {
        console.error('Get Session History Error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * Clear conversation session
 */
router.delete('/session/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;
        if (!sessionId) {
            return res.status(400).json({
                success: false,
                error: 'Missing sessionId parameter'
            });
        }
        const result = await aiService.clearSession(sessionId);
        res.json(result);
    }
    catch (error) {
        console.error('Clear Session Error:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.default = router;
