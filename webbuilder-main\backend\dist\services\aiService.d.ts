interface GenerateCodeRequest {
    prompt: string;
    projectId: string;
    sessionId?: string;
    filePath?: string;
    language?: string;
    context?: {
        existingCode?: string;
        dependencies?: string[];
        targetPlatform?: string;
    };
}
interface GenerateCodeResponse {
    success: boolean;
    generatedCode?: {
        code: string;
        language: string;
        filePath?: string;
        explanation: string;
        dependencies?: string[];
        imports?: string[];
        confidence: number;
    };
    sessionId: string;
    error?: string;
    tokensUsed?: number;
}
interface ChatRequest {
    message: string;
    projectId: string;
    sessionId?: string;
    includeContext?: boolean;
}
interface ChatResponse {
    success: boolean;
    response?: string;
    sessionId: string;
    error?: string;
    tokensUsed?: number;
    suggestedActions?: string[];
}
export declare class AIService {
    private openaiClient;
    private anthropicClient;
    private sessions;
    private defaultProvider;
    constructor();
    /**
     * Generate code from natural language prompt
     */
    generateCode(request: GenerateCodeRequest): Promise<GenerateCodeResponse>;
    /**
     * Chat with AI assistant
     */
    chat(request: ChatRequest): Promise<ChatResponse>;
    /**
     * Explain code functionality
     */
    explainCode(request: any): Promise<any>;
    /**
     * Fix code based on error message
     */
    fixCode(request: any): Promise<any>;
    /**
     * Get code improvement suggestions
     */
    getSuggestions(code: string, language: string, filePath?: string): Promise<any>;
    /**
     * Get session history
     */
    getSessionHistory(sessionId: string): Promise<any>;
    /**
     * Clear session
     */
    clearSession(sessionId: string): Promise<any>;
    /**
     * Get AI service health
     */
    getHealth(): Promise<any>;
    private generateCodeWithRealAI;
    private chatWithRealAI;
    private storeConversation;
    private generateCodeFallback;
    private chatFallback;
    private generateSessionId;
}
export {};
