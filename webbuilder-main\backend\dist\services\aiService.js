"use strict";
// AI Service Implementation with REAL OpenAI & Anthropic Integration
// FULL AI POWER ACTIVATED! 🚀
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const openai_1 = __importDefault(require("openai"));
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
class AIService {
    constructor() {
        this.openaiClient = null;
        this.anthropicClient = null;
        this.sessions = new Map();
        this.defaultProvider = 'openai';
        // Initialize OpenAI
        if (process.env.OPENAI_API_KEY) {
            this.openaiClient = new openai_1.default({
                apiKey: process.env.OPENAI_API_KEY
            });
            console.log('✅ OpenAI client initialized');
        }
        // Initialize Anthropic
        if (process.env.ANTHROPIC_API_KEY) {
            this.anthropicClient = new sdk_1.default({
                apiKey: process.env.ANTHROPIC_API_KEY
            });
            console.log('✅ Anthropic client initialized');
        }
        // Set default provider
        this.defaultProvider = process.env.DEFAULT_AI_PROVIDER || 'openai';
        console.log(`🤖 Default AI provider: ${this.defaultProvider}`);
    }
    /**
     * Generate code from natural language prompt
     */
    async generateCode(request) {
        const sessionId = request.sessionId || this.generateSessionId();
        try {
            // Try real AI first
            if (this.openaiClient || this.anthropicClient) {
                console.log(`🚀 Using REAL AI for code generation: ${request.prompt.substring(0, 50)}...`);
                return await this.generateCodeWithRealAI(request, sessionId);
            }
            // Fallback to intelligent pattern matching
            console.log('⚠️ Using fallback AI for code generation');
            return this.generateCodeFallback(request, sessionId);
        }
        catch (error) {
            console.error('Code generation error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                sessionId
            };
        }
    }
    /**
     * Chat with AI assistant
     */
    async chat(request) {
        const sessionId = request.sessionId || this.generateSessionId();
        try {
            // Try real AI first
            if (this.openaiClient || this.anthropicClient) {
                console.log(`🚀 Using REAL AI for chat: ${request.message.substring(0, 50)}...`);
                return await this.chatWithRealAI(request, sessionId);
            }
            // Fallback to intelligent responses
            console.log('⚠️ Using fallback AI for chat');
            return this.chatFallback(request, sessionId);
        }
        catch (error) {
            console.error('Chat error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                sessionId
            };
        }
    }
    /**
     * Explain code functionality
     */
    async explainCode(request) {
        const sessionId = request.sessionId || this.generateSessionId();
        return {
            success: true,
            explanation: `This ${request.language} code appears to define components and functionality. For detailed explanations, please configure an AI API key.`,
            sessionId
        };
    }
    /**
     * Fix code based on error message
     */
    async fixCode(request) {
        const sessionId = request.sessionId || this.generateSessionId();
        // Basic error pattern recognition
        let suggestedFix = request.code;
        let explanation = "Consider checking for common issues like missing imports, syntax errors, or type mismatches.";
        if (request.error.includes('Cannot find module')) {
            explanation = "This appears to be a missing import issue. Make sure the module is installed and properly imported.";
        }
        else if (request.error.includes('Unexpected token')) {
            explanation = "This looks like a syntax error. Check for missing brackets, parentheses, or semicolons.";
        }
        else if (request.error.includes('Type')) {
            explanation = "This appears to be a TypeScript type error. Check that your types match the expected interface.";
        }
        return {
            success: true,
            codeFix: {
                originalCode: request.code,
                fixedCode: suggestedFix,
                explanation,
                confidence: 0.6,
                filePath: request.filePath
            },
            sessionId
        };
    }
    /**
     * Get code improvement suggestions
     */
    async getSuggestions(code, language, filePath) {
        const suggestions = [];
        // Basic pattern analysis
        if (code.includes('console.log')) {
            suggestions.push({
                type: 'best-practice',
                title: 'Remove console.log statements',
                description: 'Console logs should be removed in production code',
                severity: 'low'
            });
        }
        if (language === 'typescript' && !code.includes('interface') && code.length > 100) {
            suggestions.push({
                type: 'best-practice',
                title: 'Consider adding type definitions',
                description: 'Adding interfaces can improve code maintainability',
                severity: 'medium'
            });
        }
        return {
            success: true,
            suggestions
        };
    }
    /**
     * Get session history
     */
    async getSessionHistory(sessionId) {
        const history = this.sessions.get(sessionId) || [];
        return {
            success: true,
            history
        };
    }
    /**
     * Clear session
     */
    async clearSession(sessionId) {
        this.sessions.delete(sessionId);
        return {
            success: true,
            message: 'Session cleared successfully'
        };
    }
    /**
     * Get AI service health
     */
    async getHealth() {
        return {
            success: true,
            providers: {
                openai: !!this.openaiClient,
                anthropic: !!this.anthropicClient,
                pinecone: false
            },
            status: (this.openaiClient || this.anthropicClient) ? 'fully-powered' : 'fallback-mode',
            defaultProvider: this.defaultProvider
        };
    }
    // Private methods for Real AI integration
    async generateCodeWithRealAI(request, sessionId) {
        try {
            // Use OpenAI as primary provider
            if (this.openaiClient) {
                const completion = await this.openaiClient.chat.completions.create({
                    model: 'gpt-3.5-turbo',
                    messages: [
                        {
                            role: 'system',
                            content: 'You are an expert React Native developer. Generate clean, production-ready code with TypeScript.'
                        },
                        {
                            role: 'user',
                            content: `Generate React Native TypeScript code for: ${request.prompt}`
                        }
                    ],
                    max_tokens: 1000,
                    temperature: 0.7
                });
                const generatedCode = completion.choices[0].message.content || '';
                return {
                    success: true,
                    generatedCode: {
                        code: generatedCode,
                        language: request.language || 'typescript',
                        explanation: 'Generated using OpenAI GPT-3.5-turbo',
                        dependencies: ['react', 'react-native'],
                        imports: ['react', 'react-native'],
                        confidence: 0.95
                    },
                    sessionId,
                    tokensUsed: completion.usage?.total_tokens
                };
            }
            // Fallback to Anthropic if available
            if (this.anthropicClient) {
                const message = await this.anthropicClient.messages.create({
                    model: 'claude-3-sonnet-20240229',
                    max_tokens: 1000,
                    messages: [{
                            role: 'user',
                            content: `Generate React Native TypeScript code for: ${request.prompt}. Provide clean, production-ready code.`
                        }]
                });
                const generatedCode = message.content[0].type === 'text' ? message.content[0].text : '';
                return {
                    success: true,
                    generatedCode: {
                        code: generatedCode,
                        language: request.language || 'typescript',
                        explanation: 'Generated using Anthropic Claude-3-Sonnet',
                        dependencies: ['react', 'react-native'],
                        imports: ['react', 'react-native'],
                        confidence: 0.95
                    },
                    sessionId
                };
            }
            throw new Error('No AI clients available');
        }
        catch (error) {
            console.error('Real AI generation failed:', error);
            // Fallback to local generation
            return this.generateCodeFallback(request, sessionId);
        }
    }
    async chatWithRealAI(request, sessionId) {
        try {
            // Use OpenAI as primary provider
            if (this.openaiClient) {
                const completion = await this.openaiClient.chat.completions.create({
                    model: 'gpt-3.5-turbo',
                    messages: [
                        {
                            role: 'system',
                            content: 'You are a helpful React Native development assistant. Provide clear, concise answers and practical advice.'
                        },
                        {
                            role: 'user',
                            content: request.message
                        }
                    ],
                    max_tokens: 500,
                    temperature: 0.7
                });
                const response = completion.choices[0].message.content || 'Sorry, I could not generate a response.';
                // Store conversation
                this.storeConversation(sessionId, request.message, response);
                return {
                    success: true,
                    response,
                    sessionId,
                    tokensUsed: completion.usage?.total_tokens
                };
            }
            // Fallback to Anthropic if available
            if (this.anthropicClient) {
                const message = await this.anthropicClient.messages.create({
                    model: 'claude-3-sonnet-20240229',
                    max_tokens: 500,
                    messages: [{
                            role: 'user',
                            content: `You are a helpful React Native development assistant. User question: ${request.message}`
                        }]
                });
                const response = message.content[0].type === 'text' ? message.content[0].text : 'Sorry, I could not generate a response.';
                // Store conversation
                this.storeConversation(sessionId, request.message, response);
                return {
                    success: true,
                    response,
                    sessionId
                };
            }
            throw new Error('No AI clients available');
        }
        catch (error) {
            console.error('Real AI chat failed:', error);
            // Fallback to local chat
            return this.chatFallback(request, sessionId);
        }
    }
    storeConversation(sessionId, userMessage, aiResponse) {
        if (!this.sessions.has(sessionId)) {
            this.sessions.set(sessionId, []);
        }
        const history = this.sessions.get(sessionId);
        history.push({ type: 'user', content: userMessage, timestamp: new Date().toISOString() }, { type: 'assistant', content: aiResponse, timestamp: new Date().toISOString() });
    }
    // Fallback implementations
    generateCodeFallback(request, sessionId) {
        const { prompt, language = 'typescript' } = request;
        const lowerPrompt = prompt.toLowerCase();
        let generatedCode = '';
        let explanation = '';
        let dependencies = [];
        // React Native Component patterns
        if (lowerPrompt.includes('button')) {
            generatedCode = `import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';

interface ButtonProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
}

const CustomButton: React.FC<ButtonProps> = ({ title, onPress, disabled = false }) => {
  return (
    <TouchableOpacity 
      style={[styles.button, disabled && styles.disabled]} 
      onPress={onPress}
      disabled={disabled}
    >
      <Text style={styles.buttonText}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  disabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CustomButton;`;
            explanation = 'Created a reusable React Native button component with TypeScript support, styling, and disabled state handling.';
            dependencies = ['react-native'];
        }
        else if (lowerPrompt.includes('screen') || lowerPrompt.includes('page')) {
            generatedCode = `import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';

const NewScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>New Screen</Text>
        <Text style={styles.subtitle}>Welcome to your new screen!</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default NewScreen;`;
            explanation = 'Created a new React Native screen component with safe area handling and basic styling.';
            dependencies = ['react-native'];
        }
        else if (lowerPrompt.includes('list') || lowerPrompt.includes('flatlist')) {
            generatedCode = `import React from 'react';
import { FlatList, View, Text, StyleSheet } from 'react-native';

interface ListItem {
  id: string;
  title: string;
  description?: string;
}

interface CustomListProps {
  data: ListItem[];
  onItemPress?: (item: ListItem) => void;
}

const CustomList: React.FC<CustomListProps> = ({ data, onItemPress }) => {
  const renderItem = ({ item }: { item: ListItem }) => (
    <View style={styles.item}>
      <Text style={styles.itemTitle}>{item.title}</Text>
      {item.description && (
        <Text style={styles.itemDescription}>{item.description}</Text>
      )}
    </View>
  );

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      style={styles.list}
      showsVerticalScrollIndicator={false}
    />
  );
};

const styles = StyleSheet.create({
  list: {
    flex: 1,
  },
  item: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
});

export default CustomList;`;
            explanation = 'Created a reusable React Native FlatList component with TypeScript support and item interaction handling.';
            dependencies = ['react-native'];
        }
        else {
            // Generic component
            generatedCode = `import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const CustomComponent: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>Custom Component</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  text: {
    fontSize: 16,
    color: '#333',
  },
});

export default CustomComponent;`;
            explanation = 'Created a basic React Native component. Provide more specific details to generate more targeted code.';
            dependencies = ['react-native'];
        }
        return {
            success: true,
            generatedCode: {
                code: generatedCode,
                language,
                explanation,
                dependencies,
                imports: ['react', 'react-native'],
                confidence: 0.8
            },
            sessionId
        };
    }
    chatFallback(request, sessionId) {
        const message = request.message.toLowerCase();
        let response = '';
        let suggestedActions = [];
        if (message.includes('hello') || message.includes('hi')) {
            response = "Hi there! I'm your AI coding assistant. I can help you build React Native components, explain code, fix errors, and generate new features. What would you like to work on?";
            suggestedActions = ['Generate a button component', 'Create a new screen', 'Add a list component'];
        }
        else if (message.includes('button')) {
            response = "I can help you create a button! Would you like a simple button, one with custom styling, or something more specific like a floating action button?";
            suggestedActions = ['Create simple button', 'Add custom styling', 'Make it interactive'];
        }
        else if (message.includes('error') || message.includes('fix')) {
            response = "I'd be happy to help fix any errors! Please share the error message and the relevant code, and I'll suggest a solution.";
            suggestedActions = ['Fix TypeScript error', 'Debug component issue', 'Check imports'];
        }
        else if (message.includes('screen') || message.includes('page')) {
            response = "Let's create a new screen! What kind of screen do you need? A login screen, list screen, detail screen, or something else?";
            suggestedActions = ['Create login screen', 'Build list screen', 'Add navigation'];
        }
        else {
            response = "I understand you want to work on your React Native app. I can help with creating components, fixing errors, adding features, or explaining code. What specifically would you like to do?";
            suggestedActions = ['Generate code', 'Fix an error', 'Explain code', 'Add a feature'];
        }
        // Store conversation history
        if (!this.sessions.has(sessionId)) {
            this.sessions.set(sessionId, []);
        }
        const history = this.sessions.get(sessionId);
        history.push({ type: 'user', content: request.message, timestamp: new Date().toISOString() }, { type: 'assistant', content: response, timestamp: new Date().toISOString() });
        return {
            success: true,
            response,
            sessionId,
            suggestedActions
        };
    }
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.AIService = AIService;
