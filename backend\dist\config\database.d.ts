export interface DatabaseInterface {
    query(text: string, params?: any[]): Promise<any>;
    close(): Promise<void>;
}
export declare let db: DatabaseInterface;
export declare const pool: {
    query: (text: string, params?: any[]) => Promise<any>;
    connect: () => Promise<{
        query: (text: string, params?: any[]) => Promise<any>;
        release: () => void;
    }>;
};
export declare const initializeDatabase: () => Promise<void>;
//# sourceMappingURL=database.d.ts.map