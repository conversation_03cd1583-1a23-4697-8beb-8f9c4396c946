"use strict";
// Bundler Service Implementation
// This will handle React Native bundling for live preview
// For MVP, provides mock responses until Metro integration is complete
Object.defineProperty(exports, "__esModule", { value: true });
exports.BundlerService = void 0;
class BundlerService {
    constructor() {
        this.bundles = new Map();
        this.watchers = new Map();
    }
    /**
     * Create bundle for a project
     */
    async createBundle(request) {
        const startTime = Date.now();
        const bundleId = `${request.projectId}-${request.platform}-${Date.now()}`;
        try {
            console.log(`Creating bundle for ${request.projectId} on ${request.platform}`);
            // For MVP, create a mock React Native Web bundle
            const mockBundle = this.generateMockBundle(request);
            // Store bundle for caching
            const cacheKey = `${request.projectId}-${request.platform}`;
            this.bundles.set(cacheKey, mockBundle);
            const buildTime = Date.now() - startTime;
            return {
                success: true,
                bundle: {
                    id: bundleId,
                    platform: request.platform,
                    code: mockBundle,
                    size: mockBundle.length,
                    buildTime,
                    createdAt: new Date().toISOString(),
                    errors: [],
                    warnings: []
                },
                buildTime
            };
        }
        catch (error) {
            const buildTime = Date.now() - startTime;
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown bundling error',
                buildTime
            };
        }
    }
    /**
     * Get cached bundle
     */
    async getCachedBundle(projectId, platform) {
        const cacheKey = `${projectId}-${platform}`;
        const cachedBundle = this.bundles.get(cacheKey);
        if (cachedBundle) {
            return {
                success: true,
                bundle: {
                    id: `${projectId}-${platform}-cached`,
                    platform,
                    code: cachedBundle,
                    size: cachedBundle.length,
                    buildTime: 0,
                    createdAt: new Date().toISOString(),
                    errors: [],
                    warnings: []
                },
                buildTime: 0
            };
        }
        // If no cached bundle, create one
        return this.createBundle({ projectId, platform });
    }
    /**
     * Validate code syntax and imports
     */
    async validateCode(request) {
        const { content, filePath } = request;
        const errors = [];
        const warnings = [];
        try {
            // Basic syntax validation
            if (filePath.endsWith('.tsx') || filePath.endsWith('.ts')) {
                // Check for common TypeScript/React Native issues
                // Check for missing imports
                if (content.includes('React.') && !content.includes('import React')) {
                    errors.push({
                        type: 'import-error',
                        message: 'React is used but not imported',
                        filename: filePath,
                        line: 1,
                        column: 1,
                        severity: 'error'
                    });
                }
                if (content.includes('View') && !content.includes('react-native')) {
                    errors.push({
                        type: 'import-error',
                        message: 'React Native components used but not imported',
                        filename: filePath,
                        line: 1,
                        column: 1,
                        severity: 'error'
                    });
                }
                // Check for common syntax issues
                const openBraces = (content.match(/{/g) || []).length;
                const closeBraces = (content.match(/}/g) || []).length;
                if (openBraces !== closeBraces) {
                    errors.push({
                        type: 'syntax-error',
                        message: 'Mismatched braces',
                        filename: filePath,
                        line: content.split('\n').length,
                        column: 1,
                        severity: 'error'
                    });
                }
                // Check for console.log (warning)
                if (content.includes('console.log')) {
                    warnings.push({
                        type: 'best-practice',
                        message: 'console.log statements should be removed in production',
                        filename: filePath,
                        line: content.indexOf('console.log'),
                        column: 1
                    });
                }
            }
            return {
                success: true,
                validation: {
                    valid: errors.length === 0,
                    errors,
                    warnings
                }
            };
        }
        catch (error) {
            return {
                success: false,
                validation: {
                    valid: false,
                    errors: [{
                            type: 'validation-error',
                            message: error instanceof Error ? error.message : 'Unknown validation error',
                            filename: filePath,
                            line: 1,
                            column: 1,
                            severity: 'error'
                        }],
                    warnings: []
                }
            };
        }
    }
    /**
     * Start file watching for a project
     */
    async startFileWatching(projectId) {
        this.watchers.set(projectId, true);
        console.log(`Started file watching for project ${projectId}`);
        return {
            success: true,
            message: `File watching started for project ${projectId}`
        };
    }
    /**
     * Stop file watching for a project
     */
    async stopFileWatching(projectId) {
        this.watchers.delete(projectId);
        console.log(`Stopped file watching for project ${projectId}`);
        return {
            success: true,
            message: `File watching stopped for project ${projectId}`
        };
    }
    /**
     * Clear bundle cache for a project
     */
    async clearBundleCache(projectId) {
        const platforms = ['web', 'ios', 'android'];
        let cleared = 0;
        for (const platform of platforms) {
            const cacheKey = `${projectId}-${platform}`;
            if (this.bundles.delete(cacheKey)) {
                cleared++;
            }
        }
        return {
            success: true,
            message: `Cleared ${cleared} cached bundles for project ${projectId}`
        };
    }
    /**
     * Cleanup bundler resources for a project
     */
    async cleanup(projectId) {
        // Stop file watching
        await this.stopFileWatching(projectId);
        // Clear bundle cache
        await this.clearBundleCache(projectId);
        return {
            success: true,
            message: `Cleaned up all resources for project ${projectId}`
        };
    }
    /**
     * Get bundler service status
     */
    async getStatus() {
        return {
            success: true,
            status: {
                activeWatchers: this.watchers.size,
                activeProcesses: 0,
                cacheSize: this.bundles.size,
                uptime: process.uptime(),
                memory: process.memoryUsage()
            }
        };
    }
    // Private helper methods
    generateMockBundle(request) {
        const { projectId, platform } = request;
        // Generate a comprehensive React Native Web bundle
        return `
// Bundle for ${projectId} (${platform})
// Generated at ${new Date().toISOString()}

(function() {
  'use strict';
  
  // React Native Web Mock Implementation
  const React = {
    createElement: function(type, props, ...children) {
      if (typeof type === 'string') {
        const element = document.createElement(type);
        if (props) {
          Object.keys(props).forEach(key => {
            if (key === 'style' && typeof props[key] === 'object') {
              Object.assign(element.style, props[key]);
            } else if (key.startsWith('on')) {
              element.addEventListener(key.slice(2).toLowerCase(), props[key]);
            } else {
              element.setAttribute(key, props[key]);
            }
          });
        }
        children.forEach(child => {
          if (typeof child === 'string') {
            element.appendChild(document.createTextNode(child));
          } else if (child) {
            element.appendChild(child);
          }
        });
        return element;
      }
      return type(props);
    },
    Fragment: function(props) {
      const fragment = document.createDocumentFragment();
      if (props.children) {
        props.children.forEach(child => {
          if (typeof child === 'string') {
            fragment.appendChild(document.createTextNode(child));
          } else if (child) {
            fragment.appendChild(child);
          }
        });
      }
      return fragment;
    }
  };
  
  // React Native Components Mock
  const ReactNative = {
    View: function(props) {
      const div = document.createElement('div');
      const defaultStyle = {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'stretch',
        flexShrink: 0,
        border: '0 solid black',
        margin: 0,
        padding: 0,
        minWidth: 0
      };
      
      if (props && props.style) {
        Object.assign(div.style, defaultStyle, props.style);
      } else {
        Object.assign(div.style, defaultStyle);
      }
      
      if (props && props.onPress) {
        div.style.cursor = 'pointer';
        div.addEventListener('click', props.onPress);
      }
      
      if (props && props.children) {
        if (Array.isArray(props.children)) {
          props.children.forEach(child => {
            if (typeof child === 'string') {
              div.appendChild(document.createTextNode(child));
            } else if (child) {
              div.appendChild(child);
            }
          });
        } else if (typeof props.children === 'string') {
          div.appendChild(document.createTextNode(props.children));
        } else if (props.children) {
          div.appendChild(props.children);
        }
      }
      
      return div;
    },
    
    Text: function(props) {
      const span = document.createElement('span');
      const defaultStyle = {
        color: 'black',
        display: 'inline',
        fontFamily: 'System',
        fontSize: '14px',
        fontWeight: 'normal',
        margin: 0,
        padding: 0,
        textDecorationLine: 'none',
        textAlign: 'left',
        wordWrap: 'break-word'
      };
      
      if (props && props.style) {
        Object.assign(span.style, defaultStyle, props.style);
      } else {
        Object.assign(span.style, defaultStyle);
      }
      
      if (props && props.onPress) {
        span.style.cursor = 'pointer';
        span.addEventListener('click', props.onPress);
      }
      
      if (props && props.children) {
        if (typeof props.children === 'string') {
          span.textContent = props.children;
        } else if (Array.isArray(props.children)) {
          props.children.forEach(child => {
            if (typeof child === 'string') {
              span.appendChild(document.createTextNode(child));
            } else if (child) {
              span.appendChild(child);
            }
          });
        }
      }
      
      return span;
    },
    
    TouchableOpacity: function(props) {
      const button = document.createElement('button');
      const defaultStyle = {
        backgroundColor: 'transparent',
        border: 'none',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '10px',
        outline: 'none'
      };
      
      if (props && props.style) {
        Object.assign(button.style, defaultStyle, props.style);
      } else {
        Object.assign(button.style, defaultStyle);
      }
      
      if (props && props.onPress) {
        button.addEventListener('click', props.onPress);
      }
      
      if (props && props.disabled) {
        button.disabled = true;
        button.style.opacity = '0.6';
      }
      
      if (props && props.children) {
        if (Array.isArray(props.children)) {
          props.children.forEach(child => {
            if (typeof child === 'string') {
              button.appendChild(document.createTextNode(child));
            } else if (child) {
              button.appendChild(child);
            }
          });
        } else if (typeof props.children === 'string') {
          button.appendChild(document.createTextNode(props.children));
        } else if (props.children) {
          button.appendChild(props.children);
        }
      }
      
      return button;
    },
    
    SafeAreaView: function(props) {
      return ReactNative.View(props);
    },
    
    ScrollView: function(props) {
      const div = ReactNative.View(props);
      div.style.overflow = 'auto';
      return div;
    }
  };
  
  // Sample App Component
  const App = function() {
    return ReactNative.View({
      style: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        padding: '20px',
        minHeight: '100vh'
      },
      children: [
        ReactNative.Text({
          style: {
            fontSize: '28px',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '16px',
            textAlign: 'center'
          },
          children: 'Welcome to Your App! \uD83D\uDE80'
        }),
        ReactNative.Text({
          style: {
            fontSize: '16px',
            color: '#666',
            textAlign: 'center',
            marginBottom: '24px',
            lineHeight: '1.5'
          },
          children: 'This is a live preview of your React Native app running in the browser.'
        }),
        ReactNative.TouchableOpacity({
          style: {
            backgroundColor: '#007AFF',
            paddingHorizontal: '24px',
            paddingVertical: '12px',
            borderRadius: '8px',
            marginBottom: '12px'
          },
          onPress: function() {
            alert('Button pressed! Your app is working correctly.');
          },
          children: ReactNative.Text({
            style: {
              color: 'white',
              fontSize: '16px',
              fontWeight: '600'
            },
            children: 'Test Button'
          })
        }),
        ReactNative.View({
          style: {
            marginTop: '20px',
            padding: '16px',
            backgroundColor: 'rgba(0, 122, 255, 0.1)',
            borderRadius: '8px',
            borderLeft: '4px solid #007AFF'
          },
          children: [
            ReactNative.Text({
              style: {
                fontSize: '14px',
                color: '#007AFF',
                fontWeight: '600',
                marginBottom: '8px'
              },
              children: '\uD83D\uDCA1 Tip'
            }),
            ReactNative.Text({
              style: {
                fontSize: '14px',
                color: '#666',
                lineHeight: '1.4'
              },
              children: 'Edit your code in the editor and see changes instantly in this preview!'
            })
          ]
        })
      ]
    });
  };
  
  // Render the app
  function renderApp() {
    const root = document.getElementById('root') || document.body;
    const app = App();
    
    // Clear existing content
    root.innerHTML = '';
    
    // Add some global styles
    const style = document.createElement('style');
    style.textContent = \`
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background-color: #f0f0f0;
      }
      * {
        box-sizing: border-box;
      }
    \`;
    document.head.appendChild(style);
    
    // Append the app
    root.appendChild(app);
    
    console.log('🚀 React Native Web app rendered successfully!');
    console.log('📱 Project: ${projectId}');
    console.log('🌐 Platform: ${platform}');
  }
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', renderApp);
  } else {
    renderApp();
  }
  
  // Export for external access
  window.ReactNativeApp = {
    React,
    ReactNative,
    App,
    renderApp
  };
  
})();
`;
    }
}
exports.BundlerService = BundlerService;
