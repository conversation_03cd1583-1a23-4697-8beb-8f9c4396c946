import bcrypt from 'bcryptjs';
import { pool } from '@/config/database';
import { generateAccessToken, generateRefreshToken, verifyRefreshToken } from '@/utils/jwt';
import { User, LoginRequest, RegisterRequest, AuthResponse, UserPreferences } from '@/types';
import { logger } from '@/utils/logger';

export class AuthService {
  private static readonly SALT_ROUNDS = 12;
  private static inMemoryUsers: Map<string, User & { passwordHash: string }> = new Map();
  private static databaseAvailable = true;

  private static async checkDatabaseConnection(): Promise<boolean> {
    try {
      await pool.query('SELECT 1');
      this.databaseAvailable = true;
      return true;
    } catch (error) {
      this.databaseAvailable = false;
      logger.warn('Database not available, using in-memory auth storage');
      return false;
    }
  }

  static async register(data: RegisterRequest): Promise<AuthResponse> {
    const { email, password, name } = data;

    try {
      const dbAvailable = await this.checkDatabaseConnection();

      if (dbAvailable) {
        return await this.registerWithDatabase(data);
      } else {
        return await this.registerInMemory(data);
      }
    } catch (error) {
      logger.error('Registration error:', error);
      throw error;
    }
  }

  private static async registerWithDatabase(data: RegisterRequest): Promise<AuthResponse> {
    const { email, password, name } = data;

    // Check if user already exists
    const existingUser = await pool.query('SELECT id FROM users WHERE email = $1', [email]);
    if (existingUser.rows.length > 0) {
      throw new Error('User with this email already exists');
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, this.SALT_ROUNDS);

    // Create user
    const userQuery = `
      INSERT INTO users (email, name, password_hash, preferences)
      VALUES ($1, $2, $3, $4)
      RETURNING id, email, name, avatar, subscription_plan, preferences, created_at, updated_at
    `;

    const defaultPreferences: UserPreferences = {
      theme: 'light',
      editorSettings: {
        fontSize: 14,
        tabSize: 2,
        wordWrap: true,
        minimap: true
      },
      defaultTemplate: 'basic',
      aiProvider: 'openai'
    };

    const result = await pool.query(userQuery, [email, name, passwordHash, JSON.stringify(defaultPreferences)]);
    const userRow = result.rows[0];

      const user: User = {
        id: userRow.id,
        email: userRow.email,
        name: userRow.name,
        avatar: userRow.avatar,
        subscriptionPlan: userRow.subscription_plan,
        apiTokens: {},
        preferences: userRow.preferences,
        createdAt: userRow.created_at,
        updatedAt: userRow.updated_at,
      };

      // Generate tokens
      const accessToken = generateAccessToken({ userId: user.id, email: user.email });
      const refreshToken = generateRefreshToken({ userId: user.id, email: user.email });

      // Store refresh token
      await this.storeRefreshToken(user.id, refreshToken);

      logger.info(`User registered successfully: ${email}`);

      return {
        user: this.sanitizeUser(user),
        accessToken,
        refreshToken,
      };
  }

  private static async registerInMemory(data: RegisterRequest): Promise<AuthResponse> {
    const { email, password, name } = data;

    // Check if user already exists
    if (this.inMemoryUsers.has(email)) {
      throw new Error('User with this email already exists');
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, this.SALT_ROUNDS);

    const defaultPreferences: UserPreferences = {
      theme: 'light',
      editorSettings: {
        fontSize: 14,
        tabSize: 2,
        wordWrap: true,
        minimap: true
      },
      defaultTemplate: 'basic',
      aiProvider: 'openai'
    };

    const user: User = {
      id: `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      email,
      name,
      avatar: undefined,
      subscriptionPlan: 'free',
      apiTokens: {},
      preferences: defaultPreferences,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store user in memory
    this.inMemoryUsers.set(email, { ...user, passwordHash });

    // Generate tokens
    const accessToken = generateAccessToken({ userId: user.id, email: user.email });
    const refreshToken = generateRefreshToken({ userId: user.id, email: user.email });

    logger.info(`User registered successfully (in-memory): ${email}`);

    return {
      user: this.sanitizeUser(user),
      accessToken,
      refreshToken,
    };
  }

  static async login(data: LoginRequest): Promise<AuthResponse> {
    const { email, password } = data;

    try {
      const dbAvailable = await this.checkDatabaseConnection();

      if (dbAvailable) {
        return await this.loginWithDatabase(data);
      } else {
        return await this.loginInMemory(data);
      }
    } catch (error) {
      logger.error('Login error:', error);
      throw error;
    }
  }

  private static async loginWithDatabase(data: LoginRequest): Promise<AuthResponse> {
    const { email, password } = data;

    // Find user
    const userQuery = `
      SELECT id, email, name, avatar, subscription_plan, password_hash, api_tokens, preferences, created_at, updated_at
      FROM users
      WHERE email = $1
    `;

    const result = await pool.query(userQuery, [email]);

    if (result.rows.length === 0) {
      throw new Error('Invalid email or password');
    }

    const userRow = result.rows[0];

    // Verify password
    const isValidPassword = await bcrypt.compare(password, userRow.password_hash);
    if (!isValidPassword) {
      throw new Error('Invalid email or password');
    }

      const user: User = {
        id: userRow.id,
        email: userRow.email,
        name: userRow.name,
        avatar: userRow.avatar,
        subscriptionPlan: userRow.subscription_plan,
        apiTokens: userRow.api_tokens || {},
        preferences: userRow.preferences || {},
        createdAt: userRow.created_at,
        updatedAt: userRow.updated_at,
      };

      // Generate tokens
      const accessToken = generateAccessToken({ userId: user.id, email: user.email });
      const refreshToken = generateRefreshToken({ userId: user.id, email: user.email });

      // Store refresh token
      await this.storeRefreshToken(user.id, refreshToken);

      logger.info(`User logged in successfully: ${email}`);

      return {
        user: this.sanitizeUser(user),
        accessToken,
        refreshToken,
      };
  }

  private static async loginInMemory(data: LoginRequest): Promise<AuthResponse> {
    const { email, password } = data;

    // Find user in memory
    const userWithPassword = this.inMemoryUsers.get(email);
    if (!userWithPassword) {
      throw new Error('Invalid email or password');
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, userWithPassword.passwordHash);
    if (!isValidPassword) {
      throw new Error('Invalid email or password');
    }

    const { passwordHash, ...user } = userWithPassword;

    // Generate tokens
    const accessToken = generateAccessToken({ userId: user.id, email: user.email });
    const refreshToken = generateRefreshToken({ userId: user.id, email: user.email });

    logger.info(`User logged in successfully (in-memory): ${email}`);

    return {
      user: this.sanitizeUser(user),
      accessToken,
      refreshToken,
    };
  }

  static async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // Verify refresh token
      const payload = verifyRefreshToken(refreshToken);
      if (!payload) {
        throw new Error('Invalid refresh token');
      }

      // Check if refresh token exists and is not revoked
      const tokenQuery = `
        SELECT id, user_id, expires_at, revoked_at
        FROM refresh_tokens 
        WHERE token = $1 AND user_id = $2
      `;
      
      const result = await pool.query(tokenQuery, [refreshToken, payload.userId]);
      
      if (result.rows.length === 0) {
        throw new Error('Refresh token not found');
      }

      const tokenRow = result.rows[0];
      
      if (tokenRow.revoked_at) {
        throw new Error('Refresh token has been revoked');
      }

      if (new Date() > new Date(tokenRow.expires_at)) {
        throw new Error('Refresh token has expired');
      }

      // Generate new tokens
      const newAccessToken = generateAccessToken({ userId: payload.userId, email: payload.email });
      const newRefreshToken = generateRefreshToken({ userId: payload.userId, email: payload.email });

      // Revoke old refresh token and store new one
      await pool.query('UPDATE refresh_tokens SET revoked_at = NOW() WHERE id = $1', [tokenRow.id]);
      await this.storeRefreshToken(payload.userId, newRefreshToken);

      return {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      };
    } catch (error) {
      logger.error('Token refresh error:', error);
      throw error;
    }
  }

  static async logout(userId: string, refreshToken?: string): Promise<void> {
    try {
      if (refreshToken) {
        // Revoke specific refresh token
        await pool.query(
          'UPDATE refresh_tokens SET revoked_at = NOW() WHERE token = $1 AND user_id = $2',
          [refreshToken, userId]
        );
      } else {
        // Revoke all refresh tokens for user
        await pool.query(
          'UPDATE refresh_tokens SET revoked_at = NOW() WHERE user_id = $1 AND revoked_at IS NULL',
          [userId]
        );
      }

      logger.info(`User logged out: ${userId}`);
    } catch (error) {
      logger.error('Logout error:', error);
      throw error;
    }
  }

  private static async storeRefreshToken(userId: string, token: string): Promise<void> {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now

    await pool.query(
      'INSERT INTO refresh_tokens (user_id, token, expires_at) VALUES ($1, $2, $3)',
      [userId, token, expiresAt]
    );
  }

  private static sanitizeUser(user: User): Omit<User, 'apiTokens'> {
    const { apiTokens, ...sanitizedUser } = user;
    return sanitizedUser;
  }

  static async cleanupExpiredTokens(): Promise<void> {
    try {
      const result = await pool.query(
        'DELETE FROM refresh_tokens WHERE expires_at < NOW() OR revoked_at IS NOT NULL'
      );
      
      if (result.rowCount && result.rowCount > 0) {
        logger.info(`Cleaned up ${result.rowCount} expired/revoked refresh tokens`);
      }
    } catch (error) {
      logger.error('Token cleanup error:', error);
    }
  }
}