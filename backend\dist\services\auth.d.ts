import { LoginRequest, RegisterRequest, AuthResponse } from '@/types';
export declare class AuthService {
    private static readonly SALT_ROUNDS;
    private static inMemoryUsers;
    private static databaseAvailable;
    private static checkDatabaseConnection;
    static register(data: RegisterRequest): Promise<AuthResponse>;
    private static registerWithDatabase;
    private static registerInMemory;
    static login(data: LoginRequest): Promise<AuthResponse>;
    private static loginWithDatabase;
    private static loginInMemory;
    static refreshToken(refreshToken: string): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
    static logout(userId: string, refreshToken?: string): Promise<void>;
    private static storeRefreshToken;
    private static sanitizeUser;
    static cleanupExpiredTokens(): Promise<void>;
}
//# sourceMappingURL=auth.d.ts.map