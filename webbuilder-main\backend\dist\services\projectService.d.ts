interface Project {
    id: string;
    name: string;
    description?: string;
    type: string;
    template?: string;
    createdAt: Date;
    updatedAt: Date;
    files?: string[];
}
interface ProjectFile {
    path: string;
    type: 'file' | 'directory';
    size?: number;
    lastModified?: Date;
}
export declare class ProjectService {
    private projectsDir;
    constructor();
    private ensureProjectsDirectory;
    /**
     * Get all projects
     */
    getAllProjects(): Promise<Project[]>;
    /**
     * Get a specific project
     */
    getProject(projectId: string): Promise<Project | null>;
    /**
     * Create a new project
     */
    createProject(options: {
        name: string;
        description?: string;
        template?: string;
        type: string;
    }): Promise<string>;
    /**
     * Update project metadata
     */
    updateProject(projectId: string, updates: Partial<Project>): Promise<void>;
    /**
     * Delete a project
     */
    deleteProject(projectId: string): Promise<void>;
    /**
     * Get project files
     */
    getProjectFiles(projectId: string): Promise<ProjectFile[]>;
    /**
     * Get file content
     */
    getFileContent(projectId: string, filePath: string): Promise<string>;
    /**
     * Save file content
     */
    saveFileContent(projectId: string, filePath: string, content: string): Promise<void>;
    /**
     * Create a new file
     */
    createFile(projectId: string, filePath: string, content?: string): Promise<void>;
    /**
     * Delete a file
     */
    deleteFile(projectId: string, filePath: string): Promise<void>;
    private generateProjectId;
    private walkDirectory;
    private createProjectStructure;
}
export {};
