import express from 'express';
import { ProjectService } from '../services/projectService';

const router = express.Router();
const projectService = new ProjectService();

/**
 * Get all projects for a user
 */
router.get('/', async (req, res) => {
  try {
    const projects = await projectService.getAllProjects();
    res.json({
      success: true,
      projects
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get a specific project
 */
router.get('/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const project = await projectService.getProject(projectId);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found'
      });
    }
    
    res.json({
      success: true,
      project
    });
  } catch (error) {
    console.error('Get project error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Create a new project
 */
router.post('/', async (req, res) => {
  try {
    const { name, description, template, type } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Project name is required'
      });
    }
    
    const projectId = await projectService.createProject({
      name,
      description,
      template,
      type: type || 'react-native'
    });
    
    const project = await projectService.getProject(projectId);
    
    res.status(201).json({
      success: true,
      project
    });
  } catch (error) {
    console.error('Create project error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Update project metadata
 */
router.put('/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const updates = req.body;
    
    await projectService.updateProject(projectId, updates);
    const project = await projectService.getProject(projectId);
    
    res.json({
      success: true,
      project
    });
  } catch (error) {
    console.error('Update project error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Delete a project
 */
router.delete('/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    await projectService.deleteProject(projectId);
    
    res.json({
      success: true,
      message: 'Project deleted successfully'
    });
  } catch (error) {
    console.error('Delete project error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get project files
 */
router.get('/:projectId/files', async (req, res) => {
  try {
    const { projectId } = req.params;
    const files = await projectService.getProjectFiles(projectId);
    
    res.json({
      success: true,
      files
    });
  } catch (error) {
    console.error('Get project files error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get file content
 */
router.get('/:projectId/file', async (req, res) => {
  try {
    const { projectId } = req.params;
    const filePath = req.query.path as string;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        error: 'File path is required'
      });
    }
    
    const content = await projectService.getFileContent(projectId, filePath);
    
    res.json({
      success: true,
      filePath,
      content
    });
  } catch (error) {
    console.error('Get file content error:', error);
    res.status(404).json({
      success: false,
      error: error instanceof Error ? error.message : 'File not found'
    });
  }
});

/**
 * Update file content
 */
router.put('/:projectId/file', async (req, res) => {
  try {
    const { projectId } = req.params;
    const filePath = req.query.path as string;
    const { content } = req.body;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        error: 'File path is required'
      });
    }
    
    if (content === undefined) {
      return res.status(400).json({
        success: false,
        error: 'File content is required'
      });
    }
    
    await projectService.saveFileContent(projectId, filePath, content);
    
    res.json({
      success: true,
      message: `File ${filePath} updated successfully`
    });
  } catch (error) {
    console.error('Update file content error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Create a new file
 */
router.post('/:projectId/files', async (req, res) => {
  try {
    const { projectId } = req.params;
    const { filePath, content = '' } = req.body;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        error: 'File path is required'
      });
    }
    
    await projectService.createFile(projectId, filePath, content);
    
    res.status(201).json({
      success: true,
      message: `File ${filePath} created successfully`
    });
  } catch (error) {
    console.error('Create file error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Delete a file
 */
router.delete('/:projectId/file', async (req, res) => {
  try {
    const { projectId } = req.params;
    const filePath = req.query.path as string;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        error: 'File path is required'
      });
    }
    
    await projectService.deleteFile(projectId, filePath);
    
    res.json({
      success: true,
      message: `File ${filePath} deleted successfully`
    });
  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
