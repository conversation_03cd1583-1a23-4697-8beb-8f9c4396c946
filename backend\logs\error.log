{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T19:54:09.918Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:33:39.331Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:33:50.152Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:34:33.918Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:39:26.098Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:39:32.784Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:41:44.647Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:43:27.840Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.155Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.158Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.171Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.478Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.494Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.498Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:47.986Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:47.993Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:48.006Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.372Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.379Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.412Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.452Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.468Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.469Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.698Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.710Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.736Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:144:5)","timestamp":"2025-07-30T22:30:19.187Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:144:5)","timestamp":"2025-07-30T22:35:10.161Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:06.306Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:11.488Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:28.913Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:34:15.105Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:35:07.352Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:35:24.417Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:35:28.946Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:35:54.898Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:38:15.937Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:38:36.104Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:40:28.694Z"}
{"level":"error","message":"Registration error: Cannot read properties of undefined (reading 'id')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'id')\n    at Function.registerWithDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:76:21)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:31:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-30T23:49:53.828Z"}
{"level":"error","message":"Registration endpoint error: Cannot read properties of undefined (reading 'id')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'id')\n    at Function.registerWithDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:76:21)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:31:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-30T23:49:53.829Z"}
{"code":"SQLITE_RANGE","errno":25,"level":"error","message":"Registration error: SQLITE_RANGE: column index out of range","service":"mobile-app-builder-backend","stack":"Error: SQLITE_RANGE: column index out of range","timestamp":"2025-07-30T23:51:04.397Z"}
{"code":"SQLITE_RANGE","errno":25,"level":"error","message":"Registration endpoint error: SQLITE_RANGE: column index out of range","service":"mobile-app-builder-backend","stack":"Error: SQLITE_RANGE: column index out of range","timestamp":"2025-07-30T23:51:04.397Z"}
