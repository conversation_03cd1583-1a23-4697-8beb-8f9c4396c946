# Backend Server Configuration
PORT=3001
NODE_ENV=development

# AI Service Configuration (Optional - fallback mode will be used if not provided)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
DEFAULT_AI_PROVIDER=openai

# Database Configuration (Future enhancement)
# DATABASE_URL=sqlite://./database.db

# CORS Configuration
CORS_ORIGIN=*

# Session Configuration
SESSION_SECRET=your_session_secret_here

# File Upload Configuration
MAX_FILE_SIZE=10mb

# Development Settings
DEBUG=true
VERBOSE_LOGGING=true
