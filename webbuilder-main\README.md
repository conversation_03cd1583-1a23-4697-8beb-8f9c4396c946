# 🚀 Noryon AI App Builder

A powerful, AI-driven React Native application builder with live preview, real-time collaboration, and intelligent code generation.

## ✨ Features

### 🤖 AI-Powered Development
- **Smart Code Generation**: Generate React Native components with natural language
- **Real-time AI Chat**: Get instant help and code suggestions
- **Code Explanation**: Understand complex code with AI explanations
- **Bug Fixing**: Automatic error detection and intelligent fixes
- **Multiple AI Providers**: OpenAI GPT-4 and Anthropic Claude support

### 🛠️ Development Environment
- **Monaco Code Editor**: Full-featured editor with TypeScript support
- **Live Preview**: Instant React Native Web preview
- **File Management**: Complete project file system
- **Real-time Collaboration**: Multiple developers, live updates
- **Project Templates**: Quick-start templates for different app types

### 🏗️ Architecture
- **Frontend**: React + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript + Socket.IO
- **AI Integration**: OpenAI API + Anthropic API
- **Code Bundling**: Custom React Native Web bundler
- **Real-time**: WebSocket-based collaboration

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Optional: OpenAI or Anthropic API keys for AI features

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd webbuilder-main

# Install frontend dependencies
npm install

# Install backend dependencies
cd backend
npm install
```

### Configuration

1. **Backend Configuration** (optional AI setup):
```bash
cd backend
cp .env.example .env
# Edit .env and add your API keys:
# OPENAI_API_KEY=your_key_here
# ANTHROPIC_API_KEY=your_key_here
```

### Development Mode

```bash
# Start both frontend and backend concurrently
npm run dev:all

# Or start individually:
# Frontend (port 5173)
npm run dev

# Backend (port 3001)
cd backend && npm run dev
```

### Production Mode

```bash
# Build and start backend
cd backend
npm run build
npm start

# Build frontend
npm run build
npm run preview
```

**Access Points:**
- Frontend: **http://localhost:5173** (dev) / **http://localhost:4173** (preview)
- Backend API: **http://localhost:3001**
- WebSocket: **ws://localhost:3001**

## 🔧 API Endpoints

### AI Services
- `POST /api/ai/chat` - Chat with AI assistant
- `POST /api/ai/generate` - Generate React Native code
- `POST /api/ai/fix` - Fix code errors
- `POST /api/ai/explain` - Explain code functionality
- `GET /api/ai/health` - AI service status

### Project Management
- `GET /api/projects` - List all projects
- `POST /api/projects` - Create new project
- `GET /api/projects/:id` - Get project details
- `PUT /api/projects/:id` - Update project
- `DELETE /api/projects/:id` - Delete project
- `GET /api/projects/:id/files` - List project files
- `GET/PUT/DELETE /api/projects/:id/files/*` - Manage files

### Code Bundling
- `POST /api/bundler/build` - Create bundle
- `GET /api/bundler/bundle/:projectId/:platform` - Get bundle
- `POST /api/bundler/validate` - Validate code
- `GET /api/bundler/status` - Bundler status

## 🌟 Usage Examples

### Creating a New Project
1. Open the app at `http://localhost:5173`
2. Navigate to `/app-builder`
3. Click "New Project" or use the AI chat
4. Start coding with live preview!

### AI Code Generation
```bash
# Example API call
curl -X POST http://localhost:3001/api/ai/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Create a login screen with email and password fields",
    "projectId": "my-project",
    "language": "typescript"
  }'
```

### Real-time Collaboration
- Multiple developers can work on the same project
- Changes are synchronized in real-time via WebSockets
- Live preview updates for all connected clients

## 🧪 Testing

```bash
# Test backend health
curl http://localhost:3001/health

# Test AI functionality
curl -X POST http://localhost:3001/api/ai/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello AI!", "projectId": "test"}'
```

## 📁 Project Structure

```
webbuilder-main/
├── src/                    # Frontend source
│   ├── components/         # React components
│   ├── services/          # API services
│   └── hooks/             # Custom hooks
├── backend/               # Backend server
│   ├── src/
│   │   ├── routes/        # API routes
│   │   ├── services/      # Business logic
│   │   └── server.ts      # Main server
│   └── projects/          # Project storage
├── public/                # Static assets
└── package.json           # Dependencies
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📜 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🎯 Roadmap

- [ ] Mobile app export (iOS/Android)
- [ ] Advanced component library
- [ ] Team collaboration features
- [ ] Version control integration
- [ ] Cloud deployment tools
- [ ] Plugin ecosystem

## 🙏 Acknowledgments

- OpenAI for GPT-4 API
- Anthropic for Claude API
- React Native community
- Monaco Editor team
- All contributors and testers

---

**Built with ❤️ by the Noryon AI team**
